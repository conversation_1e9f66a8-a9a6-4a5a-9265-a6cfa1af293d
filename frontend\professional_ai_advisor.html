<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业级AI投资顾问 - Professional AI Investment Advisor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --bg-tertiary: #2a2a2a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --accent-blue: #3b82f6;
            --accent-green: #10b981;
            --accent-red: #ef4444;
            --accent-yellow: #f59e0b;
            --accent-purple: #8b5cf6;
        }

        body {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .professional-card {
            background: linear-gradient(145deg, rgba(26, 26, 26, 0.9), rgba(42, 42, 42, 0.9));
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .score-circle {
            background: conic-gradient(from 0deg, var(--accent-blue) 0%, var(--accent-green) 50%, var(--accent-yellow) 75%, var(--accent-red) 100%);
            border-radius: 50%;
            padding: 3px;
        }

        .score-inner {
            background: var(--bg-secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .recommendation-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .strong-buy { background: linear-gradient(135deg, #10b981, #059669); }
        .buy { background: linear-gradient(135deg, #3b82f6, #2563eb); }
        .hold { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .sell { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .strong-sell { background: linear-gradient(135deg, #dc2626, #b91c1c); }

        .analysis-section {
            border-left: 4px solid var(--accent-blue);
            padding-left: 16px;
            margin: 16px 0;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid var(--accent-blue);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 顶部导航 -->
    <nav class="glass-effect border-b border-gray-700/50 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                        🤖 AI投资顾问
                    </div>
                    <div class="text-sm text-gray-400">Professional Investment Advisor</div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-400">机构级分析</div>
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 输入区域 -->
        <div class="professional-card rounded-xl p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <span class="text-2xl mr-2">🎯</span>
                股票分析
            </h2>
            <div class="flex gap-4">
                <div class="flex-1">
                    <input
                        type="text"
                        id="stockSymbol"
                        placeholder="请输入股票代码 (如: 000001, 600519)"
                        class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
                    >
                </div>
                <button
                    id="analyzeBtn"
                    onclick="startProfessionalAnalysis()"
                    class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white font-semibold rounded-lg transition-all duration-300 flex items-center space-x-2"
                >
                    <span>🔍</span>
                    <span>专业分析</span>
                </button>
            </div>
        </div>

        <!-- 分析结果区域 -->
        <div id="analysisResults" class="hidden">
            <!-- 综合评分卡片 -->
            <div class="professional-card rounded-xl p-6 mb-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 总体评分 -->
                    <div class="text-center">
                        <div class="score-circle w-32 h-32 mx-auto mb-4">
                            <div class="score-inner w-full h-full">
                                <div>
                                    <div id="overallScore" class="text-3xl font-bold">--</div>
                                    <div class="text-sm text-gray-400">综合评分</div>
                                </div>
                            </div>
                        </div>
                        <div id="recommendationBadge" class="recommendation-badge inline-block">
                            分析中...
                        </div>
                    </div>

                    <!-- 分维度评分 -->
                    <div class="col-span-2">
                        <h3 class="text-lg font-semibold mb-4">分维度评分</h3>
                        <div class="space-y-4">
                            <div class="metric-card rounded-lg p-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">技术面评分</span>
                                    <span id="technicalScore" class="text-blue-400 font-bold">--</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div id="technicalBar" class="bg-blue-400 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="metric-card rounded-lg p-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">基本面评分</span>
                                    <span id="fundamentalScore" class="text-green-400 font-bold">--</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div id="fundamentalBar" class="bg-green-400 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="metric-card rounded-lg p-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">市场环境评分</span>
                                    <span id="marketScore" class="text-yellow-400 font-bold">--</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div id="marketBar" class="bg-yellow-400 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="metric-card rounded-lg p-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">风险控制评分</span>
                                    <span id="riskScore" class="text-purple-400 font-bold">--</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div id="riskBar" class="bg-purple-400 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 专业分析报告 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- AI分析摘要 -->
                <div class="professional-card rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <span class="text-xl mr-2">🤖</span>
                        AI专业分析
                    </h3>
                    <div id="aiSummary" class="text-gray-300 leading-relaxed">
                        分析中...
                    </div>
                </div>

                <!-- 关键信号 -->
                <div class="professional-card rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <span class="text-xl mr-2">🔍</span>
                        关键技术信号
                    </h3>
                    <div id="keySignals" class="space-y-2">
                        <!-- 信号列表将在这里显示 -->
                    </div>
                </div>

                <!-- 风险警示 -->
                <div class="professional-card rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <span class="text-xl mr-2">⚠️</span>
                        风险警示
                    </h3>
                    <div id="riskWarnings" class="space-y-2">
                        <!-- 风险警示将在这里显示 -->
                    </div>
                </div>

                <!-- 投资建议 -->
                <div class="professional-card rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <span class="text-xl mr-2">💡</span>
                        投资建议
                    </h3>
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-400">目标价位:</span>
                            <span id="targetPrice" class="text-green-400 font-bold">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">预期收益:</span>
                            <span id="expectedReturn" class="text-blue-400 font-bold">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">建议持有期:</span>
                            <span id="timeHorizon" class="text-purple-400 font-bold">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">AI信心指数:</span>
                            <span id="confidenceLevel" class="text-yellow-400 font-bold">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div id="loadingState" class="hidden text-center py-12">
            <div class="loading-spinner mx-auto mb-4"></div>
            <div class="text-gray-400">正在进行专业级AI分析...</div>
            <div class="text-sm text-gray-500 mt-2">技术面 + 基本面 + 市场环境 + 风险评估</div>
        </div>
    </div>

    <script>
        // 专业级分析函数
        async function startProfessionalAnalysis() {
            const symbol = document.getElementById('stockSymbol').value.trim().toUpperCase();
            
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            // 显示加载状态
            document.getElementById('loadingState').classList.remove('hidden');
            document.getElementById('analysisResults').classList.add('hidden');
            document.getElementById('analyzeBtn').disabled = true;

            try {
                const response = await fetch(`http://localhost:8000/api/analyze/${symbol}`);
                const data = await response.json();

                if (response.ok) {
                    displayProfessionalResults(data);
                } else {
                    throw new Error(data.detail || '分析失败');
                }
            } catch (error) {
                console.error('分析错误:', error);
                alert('分析失败，请稍后重试');
            } finally {
                document.getElementById('loadingState').classList.add('hidden');
                document.getElementById('analyzeBtn').disabled = false;
            }
        }

        // 显示专业分析结果
        function displayProfessionalResults(data) {
            // 显示结果区域
            document.getElementById('analysisResults').classList.remove('hidden');
            document.getElementById('analysisResults').classList.add('fade-in');

            // 更新综合评分
            const overallScore = data.overall_score || 65;
            document.getElementById('overallScore').textContent = Math.round(overallScore);

            // 更新推荐徽章
            const recommendation = data.recommendation || 'HOLD';
            const badge = document.getElementById('recommendationBadge');
            badge.textContent = getRecommendationText(recommendation);
            badge.className = `recommendation-badge inline-block ${getRecommendationClass(recommendation)}`;

            // 更新分维度评分
            updateScoreBar('technical', data.technical_score || 50);
            updateScoreBar('fundamental', data.fundamental_score || 50);
            updateScoreBar('market', data.market_score || 65);
            updateScoreBar('risk', data.risk_score || 60);

            // 更新AI分析摘要
            document.getElementById('aiSummary').textContent = data.summary || '专业分析完成，请查看各项指标。';

            // 更新关键信号
            updateKeySignals(data.key_signals || []);

            // 更新风险警示
            updateRiskWarnings(data.risk_warnings || []);

            // 更新投资建议
            document.getElementById('targetPrice').textContent = data.target_price ? `¥${data.target_price.toFixed(2)}` : '--';
            document.getElementById('expectedReturn').textContent = data.expected_return ? `${(data.expected_return * 100).toFixed(1)}%` : '--';
            document.getElementById('timeHorizon').textContent = data.time_horizon ? `${data.time_horizon}天` : '--';
            document.getElementById('confidenceLevel').textContent = data.confidence ? `${(data.confidence * 100).toFixed(1)}%` : '--';
        }

        // 更新评分条
        function updateScoreBar(type, score) {
            document.getElementById(`${type}Score`).textContent = Math.round(score);
            setTimeout(() => {
                document.getElementById(`${type}Bar`).style.width = `${score}%`;
            }, 500);
        }

        // 更新关键信号
        function updateKeySignals(signals) {
            const container = document.getElementById('keySignals');
            container.innerHTML = signals.map(signal => `
                <div class="flex items-center space-x-2 text-sm">
                    <span class="text-blue-400">•</span>
                    <span class="text-gray-300">${signal}</span>
                </div>
            `).join('');
        }

        // 更新风险警示
        function updateRiskWarnings(warnings) {
            const container = document.getElementById('riskWarnings');
            container.innerHTML = warnings.map(warning => `
                <div class="flex items-center space-x-2 text-sm">
                    <span class="text-red-400">⚠</span>
                    <span class="text-gray-300">${warning}</span>
                </div>
            `).join('');
        }

        // 获取推荐文本
        function getRecommendationText(recommendation) {
            const texts = {
                'STRONG_BUY': '强烈买入',
                'BUY': '建议买入',
                'HOLD': '建议持有',
                'SELL': '建议卖出',
                'STRONG_SELL': '强烈卖出'
            };
            return texts[recommendation] || '建议持有';
        }

        // 获取推荐样式类
        function getRecommendationClass(recommendation) {
            const classes = {
                'STRONG_BUY': 'strong-buy',
                'BUY': 'buy',
                'HOLD': 'hold',
                'SELL': 'sell',
                'STRONG_SELL': 'strong-sell'
            };
            return classes[recommendation] || 'hold';
        }

        // 回车键触发分析
        document.getElementById('stockSymbol').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                startProfessionalAnalysis();
            }
        });
    </script>
</body>
</html>
