<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能量化分析系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
        }
        .btn-primary {
            background: linear-gradient(to right, #3b82f6, #8b5cf6);
            color: white;
            font-weight: 500;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        .btn-primary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                        <span class="text-white font-bold">📊</span>
                    </div>
                    <span class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        智能量化分析
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">专业功能 · 傻瓜操作</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 欢迎区域 -->
        <div class="text-center py-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
                <span class="text-3xl">🤖</span>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">智能量化分析</span>
                <br>
                <span class="text-2xl md:text-3xl text-gray-600 font-normal">专业功能 · 傻瓜操作</span>
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                为小白用户设计的专业量化分析平台，让复杂的股票分析变得简单易懂
            </p>
        </div>

        <!-- AI分析区域 -->
        <div class="card max-w-2xl mx-auto mb-8">
            <div class="text-center mb-6">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-4">
                    <span class="text-2xl">🚀</span>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">🤖 AI智能分析</h2>
                <p class="text-gray-600">输入股票代码，一键获得专业分析和投资建议</p>
            </div>

            <div class="space-y-4">
                <div class="relative">
                    <input 
                        type="text" 
                        id="stockSymbol" 
                        placeholder="输入股票代码 (如: AAPL, TSLA)" 
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg font-medium"
                    >
                </div>
                
                <button 
                    id="analyzeBtn" 
                    class="btn-primary w-full text-lg py-3"
                    onclick="analyzeStock()"
                >
                    🚀 开始分析
                </button>

                <!-- 快速选择 -->
                <div class="text-center">
                    <p class="text-sm text-gray-500 mb-3">热门股票快速分析</p>
                    <div class="flex flex-wrap justify-center gap-2">
                        <button onclick="setSymbol('AAPL')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">AAPL</button>
                        <button onclick="setSymbol('TSLA')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">TSLA</button>
                        <button onclick="setSymbol('GOOGL')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">GOOGL</button>
                        <button onclick="setSymbol('MSFT')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">MSFT</button>
                        <button onclick="setSymbol('AMZN')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">AMZN</button>
                        <button onclick="setSymbol('NVDA')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">NVDA</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析结果区域 -->
        <div id="analysisResult" class="hidden"></div>

        <!-- 功能介绍 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div class="card text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🤖</span>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">AI智能分析</h3>
                <p class="text-gray-600 text-sm">一键分析，AI给出明确买卖建议</p>
            </div>
            
            <div class="card text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🔮</span>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">走势预测</h3>
                <p class="text-gray-600 text-sm">机器学习预测股价走势</p>
            </div>
            
            <div class="card text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📊</span>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">策略回测</h3>
                <p class="text-gray-600 text-sm">一键回测，验证策略可靠性</p>
            </div>
            
            <div class="card text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📈</span>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">多指标分析</h3>
                <p class="text-gray-600 text-sm">智能整合多个技术指标</p>
            </div>
        </div>
    </main>

    <script>
        // 设置股票代码
        function setSymbol(symbol) {
            document.getElementById('stockSymbol').value = symbol;
        }

        // 分析股票
        async function analyzeStock() {
            const symbol = document.getElementById('stockSymbol').value.trim();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const btn = document.getElementById('analyzeBtn');
            const originalText = btn.innerHTML;
            
            // 显示加载状态
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div> AI分析中...';

            try {
                const response = await axios.get(`http://localhost:8000/api/analyze/${symbol.toUpperCase()}`);
                displayAnalysisResult(response.data);
            } catch (error) {
                console.error('分析失败:', error);
                alert('分析失败，请检查股票代码是否正确或稍后重试');
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 显示分析结果
        function displayAnalysisResult(data) {
            const resultDiv = document.getElementById('analysisResult');
            
            const recommendationColor = {
                'BUY': 'text-green-600 bg-green-50 border-green-200',
                'SELL': 'text-red-600 bg-red-50 border-red-200',
                'HOLD': 'text-yellow-600 bg-yellow-50 border-yellow-200'
            };

            const recommendationText = {
                'BUY': '🚀 建议买入',
                'SELL': '📉 建议卖出',
                'HOLD': '⏸️ 建议持有'
            };

            resultDiv.innerHTML = `
                <div class="card animate-fade-in">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">📈 ${data.symbol} 分析结果</h3>
                    
                    <!-- 基本信息 -->
                    <div class="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="text-lg font-bold text-gray-900">${data.company_name}</h4>
                            <p class="text-gray-600">${data.symbol}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-gray-900">$${data.current_price.toFixed(2)}</div>
                            <div class="text-sm ${data.price_change >= 0 ? 'text-green-600' : 'text-red-600'}">
                                ${data.price_change >= 0 ? '+' : ''}${data.price_change.toFixed(2)} (${data.price_change_percent.toFixed(2)}%)
                            </div>
                        </div>
                    </div>

                    <!-- AI建议 -->
                    <div class="text-center mb-6">
                        <div class="inline-flex items-center space-x-3 px-6 py-4 rounded-xl border-2 ${recommendationColor[data.recommendation]}">
                            <div>
                                <div class="text-lg font-bold">${recommendationText[data.recommendation]}</div>
                                <div class="text-sm opacity-80">置信度: ${(data.confidence * 100).toFixed(0)}%</div>
                            </div>
                        </div>
                    </div>

                    <!-- 智能解读 -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-6">
                        <h4 class="font-medium text-gray-900 mb-2">💡 智能解读</h4>
                        <p class="text-gray-700">${data.summary}</p>
                    </div>

                    <!-- 预期收益 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">$${data.target_price.toFixed(2)}</div>
                            <div class="text-sm text-blue-700">目标价格</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">
                                ${data.expected_return > 0 ? '+' : ''}${data.expected_return.toFixed(1)}%
                            </div>
                            <div class="text-sm text-green-700">预期收益</div>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600">${data.time_horizon}天</div>
                            <div class="text-sm text-yellow-700">投资周期</div>
                        </div>
                    </div>

                    <!-- 关键信号和风险提示 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3 flex items-center space-x-2">
                                <span>🔥 关键信号</span>
                            </h4>
                            <ul class="space-y-2">
                                ${data.key_signals.map(signal => `
                                    <li class="flex items-start space-x-2">
                                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                                        <span class="text-gray-700 text-sm">${signal}</span>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-3 flex items-center space-x-2">
                                <span>⚠️ 风险提示</span>
                            </h4>
                            <ul class="space-y-2">
                                ${data.risk_warnings.map(warning => `
                                    <li class="flex items-start space-x-2">
                                        <div class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                                        <span class="text-gray-700 text-sm">${warning}</span>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            
            resultDiv.classList.remove('hidden');
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 回车键触发分析
        document.getElementById('stockSymbol').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeStock();
            }
        });
    </script>
</body>
</html>
