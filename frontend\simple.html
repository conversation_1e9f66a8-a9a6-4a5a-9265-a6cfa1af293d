<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能量化分析系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
        }
        .btn-primary {
            background: linear-gradient(to right, #3b82f6, #8b5cf6);
            color: white;
            font-weight: 500;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        .btn-primary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                        <span class="text-white font-bold">📊</span>
                    </div>
                    <span class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        智能量化分析
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">专业功能 · 傻瓜操作</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 欢迎区域 -->
        <div class="text-center py-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
                <span class="text-3xl">🤖</span>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">智能量化分析</span>
                <br>
                <span class="text-2xl md:text-3xl text-gray-600 font-normal">专业功能 · 傻瓜操作</span>
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                为小白用户设计的专业量化分析平台，让复杂的股票分析变得简单易懂
            </p>
        </div>

        <!-- AI分析区域 -->
        <div class="card max-w-2xl mx-auto mb-8">
            <div class="text-center mb-6">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-4">
                    <span class="text-2xl">🚀</span>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">🤖 AI智能分析</h2>
                <p class="text-gray-600">输入股票代码，一键获得专业分析和投资建议</p>
            </div>

            <div class="space-y-4">
                <div class="relative">
                    <input
                        type="text"
                        id="stockSymbol"
                        placeholder="输入A股代码 (如: 000001, 600000, 300059)"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg font-medium"
                    >
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                        id="analyzeBtn"
                        class="btn-primary w-full text-lg py-3"
                        onclick="analyzeStock()"
                    >
                        🚀 AI分析
                    </button>

                    <button
                        id="predictBtn"
                        class="w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                        onclick="predictStock()"
                    >
                        🔮 走势预测
                    </button>

                    <button
                        id="backtestBtn"
                        class="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                        onclick="backtestStock()"
                    >
                        📊 策略回测
                    </button>
                </div>

                <!-- 快速选择 -->
                <div class="text-center">
                    <p class="text-sm text-gray-500 mb-3">热门A股快速分析</p>
                    <div class="flex flex-wrap justify-center gap-2">
                        <button onclick="setSymbol('000001')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">000001</button>
                        <button onclick="setSymbol('000002')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">000002</button>
                        <button onclick="setSymbol('600000')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">600000</button>
                        <button onclick="setSymbol('600036')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">600036</button>
                        <button onclick="setSymbol('600519')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">600519</button>
                        <button onclick="setSymbol('000858')" class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors">000858</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析结果区域 -->
        <div id="analysisResult" class="hidden"></div>

        <!-- 功能介绍 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div class="card text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🤖</span>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">AI智能分析</h3>
                <p class="text-gray-600 text-sm">一键分析，AI给出明确买卖建议</p>
            </div>

            <div class="card text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🔮</span>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">走势预测</h3>
                <p class="text-gray-600 text-sm">机器学习预测股价走势</p>
            </div>

            <div class="card text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📊</span>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">策略回测</h3>
                <p class="text-gray-600 text-sm">一键回测，验证策略可靠性</p>
            </div>

            <div class="card text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📈</span>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">多指标分析</h3>
                <p class="text-gray-600 text-sm">智能整合多个技术指标</p>
            </div>
        </div>
    </main>

    <script>
        // 设置股票代码
        function setSymbol(symbol) {
            document.getElementById('stockSymbol').value = symbol;
        }

        // 分析股票
        async function analyzeStock() {
            const symbol = document.getElementById('stockSymbol').value.trim();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const btn = document.getElementById('analyzeBtn');
            const originalText = btn.innerHTML;

            // 显示加载状态
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div> AI分析中...';

            try {
                const response = await axios.get(`http://localhost:8000/api/analyze/${symbol.toUpperCase()}`);
                displayAnalysisResult(response.data);
            } catch (error) {
                console.error('分析失败:', error);
                alert('分析失败，请检查股票代码是否正确或稍后重试');
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 显示分析结果
        function displayAnalysisResult(data) {
            const resultDiv = document.getElementById('analysisResult');

            const recommendationColor = {
                'BUY': 'text-green-600 bg-green-50 border-green-200',
                'SELL': 'text-red-600 bg-red-50 border-red-200',
                'HOLD': 'text-yellow-600 bg-yellow-50 border-yellow-200'
            };

            const recommendationText = {
                'BUY': '🚀 建议买入',
                'SELL': '📉 建议卖出',
                'HOLD': '⏸️ 建议持有'
            };

            resultDiv.innerHTML = `
                <div class="card animate-fade-in">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">📈 ${data.symbol} 分析结果</h3>

                    <!-- 基本信息 -->
                    <div class="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="text-lg font-bold text-gray-900">${data.company_name}</h4>
                            <p class="text-gray-600">${data.symbol}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-gray-900">¥${data.current_price.toFixed(2)}</div>
                            <div class="text-sm ${data.price_change >= 0 ? 'text-red-600' : 'text-green-600'}">
                                ${data.price_change >= 0 ? '+' : ''}${data.price_change.toFixed(2)} (${data.price_change_percent.toFixed(2)}%)
                            </div>
                        </div>
                    </div>

                    <!-- AI建议 -->
                    <div class="text-center mb-6">
                        <div class="inline-flex items-center space-x-3 px-6 py-4 rounded-xl border-2 ${recommendationColor[data.recommendation]}">
                            <div>
                                <div class="text-lg font-bold">${recommendationText[data.recommendation]}</div>
                                <div class="text-sm opacity-80">置信度: ${(data.confidence * 100).toFixed(0)}%</div>
                            </div>
                        </div>
                    </div>

                    <!-- 智能解读 -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-6">
                        <h4 class="font-medium text-gray-900 mb-2">💡 智能解读</h4>
                        <p class="text-gray-700">${data.summary}</p>
                    </div>

                    <!-- 预期收益 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">¥${data.target_price.toFixed(2)}</div>
                            <div class="text-sm text-blue-700">目标价格</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">
                                ${data.expected_return > 0 ? '+' : ''}${data.expected_return.toFixed(1)}%
                            </div>
                            <div class="text-sm text-green-700">预期收益</div>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600">${data.time_horizon}天</div>
                            <div class="text-sm text-yellow-700">投资周期</div>
                        </div>
                    </div>

                    <!-- 关键信号和风险提示 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3 flex items-center space-x-2">
                                <span>🔥 关键信号</span>
                            </h4>
                            <ul class="space-y-2">
                                ${data.key_signals.map(signal => `
                                    <li class="flex items-start space-x-2">
                                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                                        <span class="text-gray-700 text-sm">${signal}</span>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-3 flex items-center space-x-2">
                                <span>⚠️ 风险提示</span>
                            </h4>
                            <ul class="space-y-2">
                                ${data.risk_warnings.map(warning => `
                                    <li class="flex items-start space-x-2">
                                        <div class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                                        <span class="text-gray-700 text-sm">${warning}</span>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;

            resultDiv.classList.remove('hidden');
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 走势预测
        async function predictStock() {
            const symbol = document.getElementById('stockSymbol').value.trim();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const btn = document.getElementById('predictBtn');
            const originalText = btn.innerHTML;

            // 显示加载状态
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div> 预测中...';

            try {
                const response = await axios.get(`http://localhost:8000/api/predict/${symbol.toUpperCase()}`);
                displayPredictionResult(response.data);
            } catch (error) {
                console.error('预测失败:', error);
                alert('预测失败，请检查股票代码是否正确或稍后重试');
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 显示预测结果
        function displayPredictionResult(data) {
            const resultDiv = document.getElementById('analysisResult');

            const trendColor = {
                'BULLISH': 'text-green-600 bg-green-50 border-green-200',
                'BEARISH': 'text-red-600 bg-red-50 border-red-200',
                'NEUTRAL': 'text-yellow-600 bg-yellow-50 border-yellow-200'
            };

            const trendText = {
                'BULLISH': '📈 看涨趋势',
                'BEARISH': '📉 看跌趋势',
                'NEUTRAL': '➡️ 震荡趋势'
            };

            const riskColor = {
                'LOW': 'text-green-600 bg-green-50',
                'MODERATE': 'text-yellow-600 bg-yellow-50',
                'HIGH': 'text-red-600 bg-red-50'
            };

            const riskText = {
                'LOW': '🟢 低风险',
                'MODERATE': '🟡 中等风险',
                'HIGH': '🔴 高风险'
            };

            // 生成预测点的HTML
            const predictionsHtml = data.predictions.slice(0, 5).map((pred, index) => {
                const days = index + 1;
                const changePercent = ((pred.predicted_price - data.current_price) / data.current_price * 100);
                return `
                    <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600 mb-1">${days}天后</div>
                            <div class="text-2xl font-bold text-gray-900 mb-1">¥${pred.predicted_price.toFixed(2)}</div>
                            <div class="text-sm ${changePercent >= 0 ? 'text-green-600' : 'text-red-600'}">
                                ${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                置信度: ${(pred.confidence * 100).toFixed(0)}%
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            resultDiv.innerHTML = `
                <div class="card animate-fade-in">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">🔮 ${data.symbol} 走势预测</h3>

                    <!-- 当前价格 -->
                    <div class="text-center mb-6 p-4 bg-gray-50 rounded-lg">
                        <div class="text-lg text-gray-600 mb-1">当前价格</div>
                        <div class="text-3xl font-bold text-gray-900">¥${data.current_price.toFixed(2)}</div>
                    </div>

                    <!-- 预测结果 -->
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                        ${predictionsHtml}
                    </div>

                    <!-- 趋势分析 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="text-center p-4 rounded-lg border-2 ${trendColor[data.trend_direction]}">
                            <div class="text-lg font-bold mb-1">${trendText[data.trend_direction]}</div>
                            <div class="text-sm">强度: ${data.trend_strength}</div>
                        </div>

                        <div class="text-center p-4 rounded-lg ${riskColor[data.risk_level]}">
                            <div class="text-lg font-bold mb-1">${riskText[data.risk_level]}</div>
                            <div class="text-sm">投资风险评估</div>
                        </div>

                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <div class="text-lg font-bold text-purple-600 mb-1">
                                ${(data.overall_confidence * 100).toFixed(0)}%
                            </div>
                            <div class="text-sm text-purple-700">整体置信度</div>
                        </div>
                    </div>

                    <!-- 预测说明 -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-bold text-blue-900 mb-2">🧠 预测说明</h4>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li>• 基于${data.model_used}模型预测</li>
                            <li>• 预测周期: ${data.prediction_horizon}天</li>
                            <li>• 数据更新时间: ${new Date(data.generated_at).toLocaleString()}</li>
                            <li>• 预测仅供参考，投资需谨慎</li>
                        </ul>
                    </div>
                </div>
            `;

            resultDiv.classList.remove('hidden');
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 策略回测
        async function backtestStock() {
            const symbol = document.getElementById('stockSymbol').value.trim();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const btn = document.getElementById('backtestBtn');
            const originalText = btn.innerHTML;

            // 显示加载状态
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div> 回测中...';

            try {
                // 使用移动平均交叉策略进行快速回测
                const response = await axios.get(`http://localhost:8000/api/backtest/${symbol.toUpperCase()}/ma_crossover`);
                displayBacktestResult(response.data);
            } catch (error) {
                console.error('回测失败:', error);
                alert('回测失败，请检查股票代码是否正确或稍后重试');
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 显示回测结果
        function displayBacktestResult(data) {
            const resultDiv = document.getElementById('analysisResult');

            // 生成交易记录HTML
            const tradesHtml = data.trades.slice(0, 10).map(trade => {
                const actionColor = trade.action === 'BUY' ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50';
                return `
                    <div class="flex justify-between items-center p-3 ${actionColor} rounded-lg">
                        <div>
                            <span class="font-medium">${trade.action === 'BUY' ? '📈 买入' : '📉 卖出'}</span>
                            <span class="text-sm text-gray-600 ml-2">${trade.date}</span>
                        </div>
                        <div class="text-right">
                            <div class="font-bold">¥${trade.price.toFixed(2)}</div>
                            <div class="text-sm">${trade.quantity}股</div>
                        </div>
                    </div>
                `;
            }).join('');

            // 生成权益曲线点
            const equityPoints = data.equity_curve.slice(-30); // 最后30个点
            const equityHtml = equityPoints.map((point, index) => {
                const isLast = index === equityPoints.length - 1;
                const change = index > 0 ? ((point.value - equityPoints[0].value) / equityPoints[0].value * 100) : 0;
                return `
                    <div class="text-center p-2 ${isLast ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'} rounded">
                        <div class="text-xs text-gray-500">${point.date}</div>
                        <div class="font-bold text-sm">¥${point.value.toFixed(0)}</div>
                        <div class="text-xs ${change >= 0 ? 'text-green-600' : 'text-red-600'}">
                            ${change >= 0 ? '+' : ''}${change.toFixed(1)}%
                        </div>
                    </div>
                `;
            }).join('');

            resultDiv.innerHTML = `
                <div class="card animate-fade-in">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">📊 ${data.symbol} 策略回测结果</h3>

                    <!-- 策略信息 -->
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                        <h4 class="font-bold text-orange-900 mb-2">📋 回测配置</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-orange-700">策略:</span>
                                <span class="font-medium ml-1">${data.strategy}</span>
                            </div>
                            <div>
                                <span class="text-orange-700">周期:</span>
                                <span class="font-medium ml-1">${data.period}</span>
                            </div>
                            <div>
                                <span class="text-orange-700">初始资金:</span>
                                <span class="font-medium ml-1">¥10,000</span>
                            </div>
                            <div>
                                <span class="text-orange-700">手续费:</span>
                                <span class="font-medium ml-1">0.1%</span>
                            </div>
                        </div>
                    </div>

                    <!-- 绩效指标 -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                            <div class="text-2xl font-bold text-green-600">${data.performance.total_return.toFixed(1)}%</div>
                            <div class="text-sm text-green-700">总收益率</div>
                        </div>

                        <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <div class="text-2xl font-bold text-blue-600">${data.performance.sharpe_ratio.toFixed(2)}</div>
                            <div class="text-sm text-blue-700">夏普比率</div>
                        </div>

                        <div class="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                            <div class="text-2xl font-bold text-red-600">${data.performance.max_drawdown.toFixed(1)}%</div>
                            <div class="text-sm text-red-700">最大回撤</div>
                        </div>

                        <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                            <div class="text-2xl font-bold text-purple-600">${data.performance.win_rate.toFixed(1)}%</div>
                            <div class="text-sm text-purple-700">胜率</div>
                        </div>
                    </div>

                    <!-- 详细绩效 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-bold text-gray-900 mb-3">📈 交易统计</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span>总交易次数:</span>
                                    <span class="font-medium">${data.performance.total_trades}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>盈利交易:</span>
                                    <span class="font-medium text-green-600">${data.performance.winning_trades}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>亏损交易:</span>
                                    <span class="font-medium text-red-600">${data.performance.losing_trades}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>平均盈利:</span>
                                    <span class="font-medium text-green-600">${data.performance.avg_win.toFixed(2)}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>平均亏损:</span>
                                    <span class="font-medium text-red-600">${data.performance.avg_loss.toFixed(2)}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>盈亏比:</span>
                                    <span class="font-medium">${data.performance.profit_factor.toFixed(2)}</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-bold text-gray-900 mb-3">⚠️ 风险分析</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span>年化收益率:</span>
                                    <span class="font-medium">${data.performance.annual_return.toFixed(2)}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>波动率:</span>
                                    <span class="font-medium">${data.risk_analysis?.volatility?.toFixed(2) || 'N/A'}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>最大连续亏损:</span>
                                    <span class="font-medium text-red-600">${data.risk_analysis?.max_consecutive_losses || 'N/A'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>风险等级:</span>
                                    <span class="font-medium">${data.risk_analysis?.risk_level || '中等'}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 权益曲线 -->
                    <div class="mb-6">
                        <h4 class="font-bold text-gray-900 mb-3">📈 权益曲线 (最近30天)</h4>
                        <div class="grid grid-cols-6 md:grid-cols-10 gap-2">
                            ${equityHtml}
                        </div>
                    </div>

                    <!-- 交易记录 -->
                    <div class="mb-6">
                        <h4 class="font-bold text-gray-900 mb-3">📋 交易记录 (最近10笔)</h4>
                        <div class="space-y-2">
                            ${tradesHtml}
                        </div>
                    </div>

                    <!-- 策略说明 -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-bold text-blue-900 mb-2">📚 策略说明</h4>
                        <p class="text-sm text-blue-800 mb-2">${data.strategy_description}</p>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li>• 回测基于历史数据，不代表未来表现</li>
                            <li>• 实际交易需考虑滑点和市场冲击</li>
                            <li>• 建议结合多种策略分散风险</li>
                            <li>• 数据更新时间: ${new Date(data.generated_at).toLocaleString()}</li>
                        </ul>
                    </div>
                </div>
            `;

            resultDiv.classList.remove('hidden');
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 回车键触发分析
        document.getElementById('stockSymbol').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeStock();
            }
        });
    </script>
</body>
</html>
