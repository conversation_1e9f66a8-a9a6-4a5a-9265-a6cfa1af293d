import React from 'react'
import { 
  TrendingUpIcon, 
  TrendingDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { PriceChart } from './PriceChart'
import { IndicatorDashboard } from './IndicatorDashboard'

interface AnalysisData {
  symbol: string
  company_name: string
  current_price: number
  price_change: number
  price_change_percent: number
  recommendation: 'BUY' | 'SELL' | 'HOLD'
  confidence: number
  summary: string
  key_signals: string[]
  risk_warnings: string[]
  target_price: number
  expected_return: number
  time_horizon: number
  technical_indicators: any
  price_data: any[]
}

interface AnalysisResultProps {
  analysis: AnalysisData
}

export const AnalysisResult: React.FC<AnalysisResultProps> = ({ analysis }) => {
  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'BUY':
        return 'text-success-600 bg-success-50 border-success-200'
      case 'SELL':
        return 'text-danger-600 bg-danger-50 border-danger-200'
      case 'HOLD':
        return 'text-warning-600 bg-warning-50 border-warning-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'BUY':
        return <TrendingUpIcon className="w-5 h-5" />
      case 'SELL':
        return <TrendingDownIcon className="w-5 h-5" />
      case 'HOLD':
        return <ExclamationTriangleIcon className="w-5 h-5" />
      default:
        return null
    }
  }

  const getRecommendationText = (recommendation: string) => {
    switch (recommendation) {
      case 'BUY':
        return '🚀 建议买入'
      case 'SELL':
        return '📉 建议卖出'
      case 'HOLD':
        return '⏸️ 建议持有'
      default:
        return '分析中'
    }
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* 股票基本信息 */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {analysis.symbol}
            </h2>
            <p className="text-gray-600">{analysis.company_name}</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              ${analysis.current_price.toFixed(2)}
            </div>
            <div className={`flex items-center space-x-1 ${
              analysis.price_change >= 0 ? 'text-success-600' : 'text-danger-600'
            }`}>
              {analysis.price_change >= 0 ? (
                <TrendingUpIcon className="w-4 h-4" />
              ) : (
                <TrendingDownIcon className="w-4 h-4" />
              )}
              <span>
                {analysis.price_change >= 0 ? '+' : ''}
                {analysis.price_change.toFixed(2)} 
                ({analysis.price_change_percent.toFixed(2)}%)
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* AI分析结果 */}
      <div className="card">
        <div className="text-center mb-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            🤖 AI分析结果
          </h3>
          
          {/* 投资建议 */}
          <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-xl border-2 ${getRecommendationColor(analysis.recommendation)}`}>
            {getRecommendationIcon(analysis.recommendation)}
            <div>
              <div className="text-lg font-bold">
                {getRecommendationText(analysis.recommendation)}
              </div>
              <div className="text-sm opacity-80">
                置信度: {(analysis.confidence * 100).toFixed(0)}%
              </div>
            </div>
          </div>
        </div>

        {/* 一句话总结 */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h4 className="font-medium text-gray-900 mb-2">💡 智能解读</h4>
          <p className="text-gray-700">{analysis.summary}</p>
        </div>

        {/* 预期收益 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center p-4 bg-primary-50 rounded-lg">
            <div className="text-2xl font-bold text-primary-600">
              ${analysis.target_price.toFixed(2)}
            </div>
            <div className="text-sm text-primary-700">目标价格</div>
          </div>
          <div className="text-center p-4 bg-success-50 rounded-lg">
            <div className="text-2xl font-bold text-success-600">
              {analysis.expected_return > 0 ? '+' : ''}{analysis.expected_return.toFixed(1)}%
            </div>
            <div className="text-sm text-success-700">预期收益</div>
          </div>
          <div className="text-center p-4 bg-warning-50 rounded-lg">
            <div className="text-2xl font-bold text-warning-600">
              {analysis.time_horizon}天
            </div>
            <div className="text-sm text-warning-700">投资周期</div>
          </div>
        </div>

        {/* 关键信号 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <CheckCircleIcon className="w-5 h-5 text-success-600" />
              <span>🔥 关键信号</span>
            </h4>
            <ul className="space-y-2">
              {analysis.key_signals.map((signal, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-success-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-700 text-sm">{signal}</span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <XCircleIcon className="w-5 h-5 text-danger-600" />
              <span>⚠️ 风险提示</span>
            </h4>
            <ul className="space-y-2">
              {analysis.risk_warnings.map((warning, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-danger-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-700 text-sm">{warning}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* 价格图表 */}
      <div className="card">
        <h3 className="text-lg font-bold text-gray-900 mb-4">📈 价格走势</h3>
        <PriceChart data={analysis.price_data} symbol={analysis.symbol} />
      </div>

      {/* 技术指标仪表板 */}
      <div className="card">
        <h3 className="text-lg font-bold text-gray-900 mb-4">📊 技术指标</h3>
        <IndicatorDashboard indicators={analysis.technical_indicators} />
      </div>
    </div>
  )
}
