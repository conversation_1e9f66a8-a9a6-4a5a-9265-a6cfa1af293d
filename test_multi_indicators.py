#!/usr/bin/env python3
"""
测试多指标分析功能
"""

import requests
import json

def test_multi_indicators_api():
    """测试多指标分析API"""
    print("📊 测试多指标分析功能")
    print("="*50)
    
    base_url = "http://localhost:8000"
    
    # 首先测试基础API是否正常
    print("🔍 测试基础API...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 基础API正常")
        else:
            print(f"❌ 基础API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False
    
    # 测试API文档
    print("📚 检查API文档...")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档可访问")
        else:
            print(f"❌ API文档异常: {response.status_code}")
    except Exception as e:
        print(f"❌ API文档访问失败: {e}")
    
    # 测试现有的分析API
    print("🔍 测试现有分析API...")
    try:
        response = requests.get(f"{base_url}/api/analyze/000001", timeout=30)
        if response.status_code == 200:
            print("✅ 基础分析API正常")
        else:
            print(f"❌ 基础分析API异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 基础分析API失败: {e}")
    
    # 测试多指标分析API
    print("📈 测试多指标分析API...")
    test_symbols = ['000001', '600519']
    
    for symbol in test_symbols:
        print(f"\n🔍 测试 {symbol}:")
        
        try:
            # 尝试不同的路径
            paths_to_try = [
                f"/api/analyze/multi-indicators/{symbol}",
                f"/api/multi-indicators/{symbol}",
                f"/api/analyze/indicators/{symbol}"
            ]
            
            for path in paths_to_try:
                print(f"   尝试路径: {path}")
                response = requests.get(f"{base_url}{path}", timeout=30)
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ 成功! 数据字段: {list(data.keys())}")
                    
                    # 检查关键字段
                    if 'detailed_indicators' in data:
                        indicators = data['detailed_indicators']
                        print(f"   📊 指标数量: {len(indicators)}")
                        print(f"   📊 指标类型: {list(indicators.keys())}")
                    
                    if 'overall_score' in data:
                        score = data['overall_score']
                        print(f"   🎯 综合评分: {score.get('overall_score', 'N/A')}")
                        print(f"   🎯 综合信号: {score.get('overall_signal', 'N/A')}")
                    
                    return True
                    
                elif response.status_code == 404:
                    print(f"   ❌ 路径不存在")
                else:
                    print(f"   ❌ 错误: {response.status_code}")
                    if response.text:
                        print(f"   错误信息: {response.text[:200]}")
                        
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
    
    return False

def check_api_routes():
    """检查API路由"""
    print("\n📋 检查API路由...")
    
    try:
        response = requests.get("http://localhost:8000/openapi.json", timeout=10)
        if response.status_code == 200:
            openapi_data = response.json()
            paths = openapi_data.get('paths', {})
            
            print("🔍 可用的API路径:")
            for path in sorted(paths.keys()):
                if 'analyze' in path or 'indicator' in path:
                    methods = list(paths[path].keys())
                    print(f"   {path} [{', '.join(methods)}]")
            
            # 检查是否有多指标相关的路径
            multi_indicator_paths = [p for p in paths.keys() if 'multi' in p or 'indicators' in p]
            if multi_indicator_paths:
                print(f"\n📈 多指标相关路径: {multi_indicator_paths}")
            else:
                print("\n⚠️  未找到多指标相关路径")
                
        else:
            print(f"❌ 无法获取API规范: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查API路由失败: {e}")

def main():
    """主函数"""
    print("🧪 多指标分析功能测试")
    print("="*60)
    
    # 检查API路由
    check_api_routes()
    
    # 测试多指标分析
    success = test_multi_indicators_api()
    
    print("\n" + "="*60)
    print("📋 测试结果总结")
    print("="*60)
    
    if success:
        print("✅ 多指标分析功能正常工作")
        print("🎯 功能特色:")
        print("   - 详细技术指标分析")
        print("   - 综合评分系统")
        print("   - 信号强度分析")
        print("   - 趋势分析")
    else:
        print("❌ 多指标分析功能存在问题")
        print("🔧 可能的原因:")
        print("   - API路由未正确注册")
        print("   - 后端代码存在错误")
        print("   - 依赖模块导入失败")
        print("   - 服务器重启问题")
        
        print("\n💡 建议解决方案:")
        print("   1. 检查服务器日志中的错误信息")
        print("   2. 验证API路由注册是否正确")
        print("   3. 检查A股分析器代码是否有语法错误")
        print("   4. 重启服务器并重新测试")
    
    print("\n🎉 测试完成!")
    return 0 if success else 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
