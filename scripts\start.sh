#!/bin/bash

# 智能量化分析系统启动脚本

echo "🚀 启动智能量化分析系统..."

# 检查是否安装了必要的工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查Node.js
check_command "node"
echo "✅ Node.js 已安装: $(node --version)"

# 检查Python
check_command "python"
echo "✅ Python 已安装: $(python --version)"

# 检查npm
check_command "npm"
echo "✅ npm 已安装: $(npm --version)"

# 创建必要的目录
mkdir -p logs data models

# 复制环境配置文件
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📝 已创建 .env 文件，请根据需要修改配置"
fi

# 安装前端依赖
echo "📦 安装前端依赖..."
cd frontend
npm install
cd ..

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
pip install -r requirements.txt
cd ..

# 启动服务
echo "🎯 启动服务..."

# 启动后端
echo "🔧 启动后端服务..."
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 5

# 启动前端
echo "🎨 启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 系统启动完成！"
echo ""
echo "📱 前端地址: http://localhost:3000"
echo "🔧 后端地址: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
wait

# 清理进程
echo "🛑 正在停止服务..."
kill $BACKEND_PID 2>/dev/null
kill $FRONTEND_PID 2>/dev/null
echo "✅ 服务已停止"
