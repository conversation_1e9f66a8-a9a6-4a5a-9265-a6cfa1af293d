import React from 'react'
import { Link } from 'react-router-dom'
import { 
  SparklesIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  BackwardIcon,
  RocketLaunchIcon,
  LightBulbIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline'

export const HomePage: React.FC = () => {
  const features = [
    {
      icon: SparklesIcon,
      title: '🤖 AI智能分析',
      description: '一键分析，AI给出明确买卖建议',
      link: '/analysis',
      color: 'from-blue-500 to-purple-600'
    },
    {
      icon: ArrowTrendingUpIcon,
      title: '🔮 走势预测',
      description: '机器学习预测股价走势',
      link: '/prediction',
      color: 'from-green-500 to-teal-600'
    },
    {
      icon: BackwardIcon,
      title: '📊 策略回测',
      description: '一键回测，验证策略可靠性',
      link: '/backtest',
      color: 'from-orange-500 to-red-600'
    },
    {
      icon: ChartBarIcon,
      title: '📈 多指标分析',
      description: '智能整合多个技术指标',
      link: '/analysis',
      color: 'from-purple-500 to-pink-600'
    }
  ]

  const advantages = [
    {
      icon: RocketLaunchIcon,
      title: '一键启动',
      description: '复杂分析一键完成，无需专业知识'
    },
    {
      icon: LightBulbIcon,
      title: '结果导向',
      description: '直接给出投资建议，隐藏技术细节'
    },
    {
      icon: ShieldCheckIcon,
      title: '风险可控',
      description: '自动风险评估和智能提醒'
    }
  ]

  return (
    <div className="space-y-12">
      {/* 英雄区域 */}
      <div className="text-center py-12">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-primary-600 to-purple-600 rounded-full mb-6">
          <SparklesIcon className="w-10 h-10 text-white" />
        </div>
        
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          <span className="text-gradient">智能量化分析</span>
          <br />
          <span className="text-2xl md:text-3xl text-gray-600 font-normal">
            专业功能 · 傻瓜操作
          </span>
        </h1>
        
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          为小白用户设计的专业量化分析平台，让复杂的股票分析变得简单易懂
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/analysis"
            className="btn-primary text-lg px-8 py-4 inline-flex items-center space-x-2"
          >
            <RocketLaunchIcon className="w-5 h-5" />
            <span>开始分析</span>
          </Link>
          <Link
            to="/prediction"
            className="btn-secondary text-lg px-8 py-4 inline-flex items-center space-x-2"
          >
            <ArrowTrendingUpIcon className="w-5 h-5" />
            <span>走势预测</span>
          </Link>
        </div>
      </div>

      {/* 核心功能 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {features.map((feature, index) => {
          const Icon = feature.icon
          return (
            <Link
              key={index}
              to={feature.link}
              className="group card hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
            >
              <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                <Icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </Link>
          )
        })}
      </div>

      {/* 设计理念 */}
      <div className="card bg-gradient-to-r from-primary-50 to-purple-50 border-primary-200">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            🎯 设计理念
          </h2>
          <p className="text-lg text-gray-600">
            让量化投资变得简单易懂
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {advantages.map((advantage, index) => {
            const Icon = advantage.icon
            return (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                  <Icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {advantage.title}
                </h3>
                <p className="text-gray-600">
                  {advantage.description}
                </p>
              </div>
            )
          })}
        </div>
      </div>

      {/* 使用流程 */}
      <div className="card">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            🎮 使用流程
          </h2>
          <p className="text-lg text-gray-600">
            极简三步操作，专业分析结果
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[
            { step: '1', title: '输入股票代码', desc: '系统自动获取数据', emoji: '📝' },
            { step: '2', title: '查看AI分析', desc: '获得明确投资建议', emoji: '🤖' },
            { step: '3', title: '一键回测', desc: '验证策略可靠性', emoji: '📊' },
            { step: '4', title: '跟踪预测', desc: '监控走势变化', emoji: '👀' }
          ].map((item, index) => (
            <div key={index} className="text-center">
              <div className="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                {item.step}
              </div>
              <div className="text-2xl mb-2">{item.emoji}</div>
              <h3 className="font-bold text-gray-900 mb-1">{item.title}</h3>
              <p className="text-sm text-gray-600">{item.desc}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
