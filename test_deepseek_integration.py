#!/usr/bin/env python3
"""
测试DeepSeek大模型集成
"""

import os
import sys
import asyncio
import requests
import json
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加backend路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

def test_deepseek_api_config():
    """测试DeepSeek API配置"""
    print("🔍 测试DeepSeek API配置")
    print("="*50)

    # 检查环境变量
    api_key = os.getenv("DEEPSEEK_API_KEY")

    if api_key and api_key != "your-deepseek-api-key-here":
        print(f"✅ DeepSeek API密钥已配置: {api_key[:10]}...")
        return True
    else:
        print("❌ DeepSeek API密钥未配置")
        print("   请在.env文件中设置: DEEPSEEK_API_KEY=your-actual-api-key")
        return False

def test_ai_analyzer_import():
    """测试AI分析器导入"""
    print("\n🔍 测试AI分析器导入")
    print("="*50)

    try:
        from app.services.ai_analyzer import AIAnalyzer, DeepSeekClient
        print("✅ AI分析器导入成功")

        # 测试DeepSeek客户端初始化
        client = DeepSeekClient()
        print(f"✅ DeepSeek客户端初始化成功")
        print(f"   API密钥状态: {'已配置' if client.api_key else '未配置'}")
        print(f"   模型: {client.model}")
        print(f"   API地址: {client.base_url}")

        # 测试AI分析器初始化
        analyzer = AIAnalyzer()
        print(f"✅ AI分析器初始化成功")
        print(f"   使用大模型: {'是' if analyzer.use_llm else '否'}")
        print(f"   模型加载状态: {'成功' if analyzer.models_loaded else '失败'}")

        return True

    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

async def test_deepseek_api_call():
    """测试DeepSeek API调用"""
    print("\n🔍 测试DeepSeek API调用")
    print("="*50)

    try:
        from app.services.ai_analyzer import DeepSeekClient

        client = DeepSeekClient()

        if not client.api_key:
            print("⚠️ API密钥未配置，跳过API调用测试")
            return False

        # 构建测试提示词
        test_prompt = """
请分析以下股票：
股票代码: 000001
公司名称: 平安银行
当前价格: ¥13.50
今日涨跌幅: +2.3%

技术指标:
- RSI: BUY(MODERATE) 值:65.2
- MACD: BUY(WEAK) 值:0.15
- MA: HOLD(MODERATE) 值:13.2

请给出投资建议。
"""

        print("📤 发送测试请求...")
        response = await client.generate_analysis(test_prompt)

        if response:
            print("✅ DeepSeek API调用成功")
            print(f"📝 响应长度: {len(response)} 字符")
            print(f"📄 响应预览: {response[:200]}...")
            return True
        else:
            print("❌ DeepSeek API调用失败")
            return False

    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return False

async def test_ai_analysis():
    """测试完整AI分析流程"""
    print("\n🔍 测试完整AI分析流程")
    print("="*50)

    try:
        from app.services.ai_analyzer import AIAnalyzer

        analyzer = AIAnalyzer()

        # 模拟股票数据
        stock_data = {
            'symbol': '000001',
            'company_name': '平安银行',
            'current_price': 13.50,
            'price_change_percent': 2.3
        }

        # 模拟技术指标
        indicators = {
            'rsi': {
                'signal': 'BUY',
                'strength': 'MODERATE',
                'value': 65.2,
                'description': 'RSI处于超买区间下沿'
            },
            'macd': {
                'signal': 'BUY',
                'strength': 'WEAK',
                'value': 0.15,
                'description': 'MACD金叉形成'
            },
            'ma': {
                'signal': 'HOLD',
                'strength': 'MODERATE',
                'value': 13.2,
                'description': '价格在均线附近震荡'
            }
        }

        print("📊 执行AI分析...")
        result = await analyzer.analyze_stock('000001', stock_data, indicators)

        print("✅ AI分析完成")
        print(f"📈 推荐操作: {result.get('recommendation', 'N/A')}")
        print(f"🎯 置信度: {result.get('confidence', 0)*100:.1f}%")
        print(f"📝 分析摘要: {result.get('summary', 'N/A')[:100]}...")
        print(f"🔑 关键信号: {len(result.get('key_signals', []))}个")
        print(f"⚠️ 风险提示: {len(result.get('risk_warnings', []))}个")
        print(f"💰 目标价格: ¥{result.get('target_price', 0):.2f}")
        print(f"📊 预期收益: {result.get('expected_return', 0):.1f}%")
        print(f"⏰ 持有期: {result.get('time_horizon', 0)}天")

        return True

    except Exception as e:
        print(f"❌ AI分析失败: {e}")
        return False

def test_backend_api():
    """测试后端API"""
    print("\n🔍 测试后端API")
    print("="*50)

    try:
        # 测试AI分析API
        response = requests.get("http://localhost:8000/api/analyze/000001", timeout=30)

        if response.status_code == 200:
            data = response.json()
            print("✅ 后端AI分析API正常")
            print(f"📈 推荐操作: {data.get('recommendation', 'N/A')}")
            print(f"🎯 置信度: {data.get('confidence', 0)*100:.1f}%")
            print(f"📝 分析摘要: {data.get('summary', 'N/A')[:100]}...")

            # 检查是否使用了大模型
            summary = data.get('summary', '')
            if 'DeepSeek' in summary or len(summary) > 200:
                print("🤖 检测到大模型分析结果")
            else:
                print("🔧 使用规则引擎分析结果")

            return True
        else:
            print(f"❌ 后端API错误: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 后端API测试失败: {e}")
        return False

def print_setup_guide():
    """打印设置指南"""
    print("\n📖 DeepSeek API设置指南")
    print("="*50)

    print("🎯 获取DeepSeek API密钥:")
    print("   1. 访问 https://platform.deepseek.com/")
    print("   2. 注册账户并登录")
    print("   3. 进入API密钥管理页面")
    print("   4. 创建新的API密钥")
    print("   5. 复制API密钥")

    print("\n🔧 配置API密钥:")
    print("   1. 打开项目根目录的 .env 文件")
    print("   2. 找到 DEEPSEEK_API_KEY=your-deepseek-api-key-here")
    print("   3. 将 your-deepseek-api-key-here 替换为实际的API密钥")
    print("   4. 保存文件并重启后端服务")

    print("\n💡 使用说明:")
    print("   • 配置API密钥后，系统将自动使用DeepSeek大模型")
    print("   • 如果API调用失败，系统会自动降级到规则引擎")
    print("   • 大模型分析结果更加智能和个性化")
    print("   • API调用会产生费用，请注意使用量")

async def main():
    """主函数"""
    print("🤖 DeepSeek大模型集成测试")
    print("="*60)

    # 测试配置
    config_ok = test_deepseek_api_config()

    # 测试导入
    import_ok = test_ai_analyzer_import()

    # 测试API调用（如果配置了密钥）
    api_ok = False
    if config_ok:
        api_ok = await test_deepseek_api_call()

    # 测试AI分析
    analysis_ok = await test_ai_analysis()

    # 测试后端API
    backend_ok = test_backend_api()

    # 打印设置指南
    print_setup_guide()

    # 总结
    print("\n" + "="*60)
    print("📋 DeepSeek集成测试总结")
    print("="*60)

    print("✅ 测试结果:")
    print(f"   {'✅' if config_ok else '❌'} API密钥配置: {'已配置' if config_ok else '未配置'}")
    print(f"   {'✅' if import_ok else '❌'} 代码导入: {'成功' if import_ok else '失败'}")
    print(f"   {'✅' if api_ok else '❌'} API调用: {'成功' if api_ok else '失败/跳过'}")
    print(f"   {'✅' if analysis_ok else '❌'} AI分析: {'成功' if analysis_ok else '失败'}")
    print(f"   {'✅' if backend_ok else '❌'} 后端API: {'成功' if backend_ok else '失败'}")

    print("\n🎯 集成状态:")
    if config_ok and api_ok:
        print("   🤖 DeepSeek大模型已成功集成并可正常使用")
        print("   📊 AI分析报告将使用大模型生成")
    elif import_ok and analysis_ok:
        print("   🔧 DeepSeek集成代码正常，但使用规则引擎模式")
        print("   💡 配置API密钥后即可启用大模型分析")
    else:
        print("   ❌ DeepSeek集成存在问题，请检查代码")

    print("\n🚀 下一步:")
    if not config_ok:
        print("   1. 获取DeepSeek API密钥")
        print("   2. 在.env文件中配置API密钥")
        print("   3. 重启后端服务")
    else:
        print("   1. 测试AI分析功能")
        print("   2. 体验大模型生成的分析报告")
        print("   3. 监控API使用量和费用")

    return 0

if __name__ == '__main__':
    import sys
    sys.exit(asyncio.run(main()))
