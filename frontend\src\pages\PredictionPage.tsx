import React, { useState } from 'react'
import { useParams } from 'react-router-dom'
import { ArrowTrendingUpIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { toast } from 'react-hot-toast'

export const PredictionPage: React.FC = () => {
  const { symbol: urlSymbol } = useParams()
  const [symbol, setSymbol] = useState(urlSymbol || '')
  const [loading, setLoading] = useState(false)
  const [prediction, setPrediction] = useState<any>(null)

  const handlePredict = async () => {
    if (!symbol.trim()) {
      toast.error('请输入股票代码')
      return
    }

    setLoading(true)
    try {
      // TODO: 实现预测API调用
      await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用
      
      // 模拟预测数据
      setPrediction({
        symbol: symbol.toUpperCase(),
        current_price: 150.25,
        predictions: [
          { days: 7, price: 155.30, confidence: 0.85, direction: 'UP' },
          { days: 14, price: 162.45, confidence: 0.78, direction: 'UP' },
          { days: 30, price: 158.90, confidence: 0.65, direction: 'UP' },
        ],
        trend: 'BULLISH',
        risk_level: 'MODERATE'
      })
      
      toast.success('预测完成！')
    } catch (err) {
      toast.error('预测失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handlePredict()
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          🔮 走势预测
        </h1>
        <p className="text-gray-600">
          基于机器学习的股价走势预测
        </p>
      </div>

      {/* 输入区域 */}
      <div className="card">
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-600 to-teal-600 rounded-full mb-4">
            <ArrowTrendingUpIcon className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            AI预测引擎
          </h2>
          <p className="text-gray-600">
            多模型融合，智能预测股价走势
          </p>
        </div>

        <div className="max-w-md mx-auto">
          <div className="relative">
            <input
              type="text"
              value={symbol}
              onChange={(e) => setSymbol(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入股票代码 (如: AAPL, TSLA)"
              className="input-field pr-12 text-center text-lg font-medium"
              disabled={loading}
            />
            <button
              onClick={handlePredict}
              disabled={loading || !symbol.trim()}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-green-600 hover:text-green-700 disabled:text-gray-400 transition-colors duration-200"
            >
              {loading ? (
                <LoadingSpinner />
              ) : (
                <MagnifyingGlassIcon className="w-5 h-5" />
              )}
            </button>
          </div>

          <button
            onClick={handlePredict}
            disabled={loading || !symbol.trim()}
            className="w-full mt-4 bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <span className="flex items-center justify-center space-x-2">
                <LoadingSpinner />
                <span>AI预测中...</span>
              </span>
            ) : (
              '🔮 开始预测'
            )}
          </button>
        </div>
      </div>

      {/* 预测结果 */}
      {prediction && (
        <div className="space-y-6 animate-fade-in">
          <div className="card">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              📈 {prediction.symbol} 预测结果
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {prediction.predictions.map((pred: any, index: number) => (
                <div key={index} className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600 mb-1">
                      {pred.days}天后
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      ${pred.price.toFixed(2)}
                    </div>
                    <div className="text-sm text-green-700">
                      置信度: {(pred.confidence * 100).toFixed(0)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">📊 趋势分析</h4>
                <p className="text-blue-700">
                  {prediction.trend === 'BULLISH' ? '看涨趋势' : '看跌趋势'}
                </p>
              </div>
              <div className="p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">⚠️ 风险等级</h4>
                <p className="text-yellow-700">
                  {prediction.risk_level === 'MODERATE' ? '中等风险' : '高风险'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 功能说明 */}
      <div className="card bg-gradient-to-r from-green-50 to-teal-50 border-green-200">
        <h3 className="text-lg font-bold text-gray-900 mb-4">
          🧠 预测原理
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">📚 机器学习模型</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• LSTM神经网络</li>
              <li>• 随机森林算法</li>
              <li>• 支持向量机</li>
              <li>• 集成学习方法</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">📊 数据特征</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 历史价格数据</li>
              <li>• 技术指标</li>
              <li>• 成交量分析</li>
              <li>• 市场情绪指标</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
