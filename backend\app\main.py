from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager

from app.api.routes import analysis, prediction, backtest
from app.core.config import settings
from app.core.logging import setup_logging

# 设置日志
setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    print("🚀 智能量化分析系统启动中...")
    yield
    # 关闭时执行
    print("📴 系统正在关闭...")

# 创建FastAPI应用
app = FastAPI(
    title="智能量化分析系统",
    description="专为小白用户设计的专业量化分析API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(analysis.router, prefix="/api", tags=["分析"])
app.include_router(prediction.router, prefix="/api", tags=["预测"])
app.include_router(backtest.router, prefix="/api", tags=["回测"])

@app.get("/")
async def root():
    """根路径 - 系统状态"""
    return {
        "message": "🤖 智能量化分析系统",
        "description": "专业功能 · 傻瓜操作",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z"
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "detail": "服务器内部错误，请稍后重试",
            "error": str(exc) if settings.DEBUG else "Internal Server Error"
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
