<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A股量化分析系统</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;
            --border-primary: #2a2a2a;
            --border-secondary: #333333;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;
            --accent-primary: #3b82f6;
            --accent-secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
        }

        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .btn-primary {
            @apply bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 border border-blue-500/20 hover:border-blue-400/40 shadow-lg hover:shadow-blue-500/25;
        }

        .btn-secondary {
            @apply bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 border border-purple-500/20 hover:border-purple-400/40 shadow-lg hover:shadow-purple-500/25;
        }

        .btn-success {
            @apply bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 border border-green-500/20 hover:border-green-400/40 shadow-lg hover:shadow-green-500/25;
        }

        .btn-warning {
            @apply bg-gradient-to-r from-orange-600 to-orange-500 hover:from-orange-500 hover:to-orange-400 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 border border-orange-500/20 hover:border-orange-400/40 shadow-lg hover:shadow-orange-500/25;
        }

        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: 12px;
            padding: 24px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .card:hover {
            border-color: var(--border-secondary);
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .input-field {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            color: var(--text-primary);
            border-radius: 8px;
            padding: 12px 16px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .input-field::placeholder {
            color: var(--text-muted);
        }

        .loading-spinner {
            @apply inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin;
        }

        .animate-fade-in {
            animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-effect {
            background: rgba(17, 17, 17, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-primary);
        }

        .metric-card {
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            border-color: var(--border-secondary);
            transform: translateY(-1px);
        }

        .quick-btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            color: var(--text-secondary);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .quick-btn:hover {
            background: var(--bg-secondary);
            border-color: var(--border-secondary);
            color: var(--text-primary);
        }

        .signal-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .signal-buy {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .signal-sell {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .signal-neutral {
            background: rgba(156, 163, 175, 0.2);
            color: #9ca3af;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }

        .strength-strong {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        .strength-moderate {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .strength-weak {
            background: rgba(156, 163, 175, 0.2);
            color: #9ca3af;
        }

        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        .btn-favorite {
            @apply bg-gradient-to-r from-pink-600 to-rose-500 hover:from-pink-500 hover:to-rose-400 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 border border-pink-500/20 hover:border-pink-400/40 shadow-lg hover:shadow-pink-500/25;
        }

        .btn-favorite.favorited {
            @apply bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-500 hover:to-pink-500;
        }

        .btn-favorite.favorited svg {
            fill: currentColor;
        }

        .btn-manage-favorites {
            @apply bg-gradient-to-r from-gray-600 to-gray-500 hover:from-gray-500 hover:to-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 border border-gray-500/20 hover:border-gray-400/40 shadow-lg hover:shadow-gray-500/25;
        }

        .favorite-stock-btn {
            @apply bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 border border-gray-700/50 hover:border-gray-600/50;
        }

        .favorite-stock-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .favorite-stock-btn.analyzing {
            @apply bg-blue-600/20 border-blue-500/30 text-blue-400;
        }

        #favoritesPanel {
            @apply bg-gray-900/30 rounded-lg p-4 border border-gray-700/50 backdrop-blur-sm;
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 头部 -->
    <header class="glass-effect sticky top-0 z-50">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">AI</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold gradient-text">A股量化分析</h1>
                        <p class="text-xs text-gray-500">Professional Trading Intelligence</p>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-4 text-sm text-gray-400">
                    <span>实时数据</span>
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-6 py-8">
        <!-- 欢迎区域 -->
        <div class="text-center py-16">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl mb-6">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                <span class="gradient-text">智能量化分析</span>
            </h1>
            <p class="text-lg text-gray-400 mb-8 max-w-2xl mx-auto">
                专业级A股技术分析平台，为投资者提供精准的量化分析和智能投资建议
            </p>
        </div>

        <!-- 分析区域 -->
        <div class="max-w-4xl mx-auto">
            <div class="card mb-8">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold mb-2">股票分析</h2>
                    <p class="text-gray-400">输入A股代码，获取专业分析报告</p>
                </div>

                <div class="space-y-6">
                    <!-- 输入区域 -->
                    <div class="relative">
                        <input
                            type="text"
                            id="stockSymbol"
                            placeholder="请输入A股代码 (如: 000001, 600519)"
                            class="input-field w-full text-center text-lg font-medium"
                        >
                    </div>

                    <!-- 操作按钮 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button
                            id="analyzeBtn"
                            class="btn-primary w-full text-lg py-3"
                            onclick="analyzeStock()"
                        >
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            AI分析
                        </button>

                        <button
                            id="predictBtn"
                            class="btn-success w-full text-lg py-3"
                            onclick="predictStock()"
                        >
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            走势预测
                        </button>

                        <button
                            id="backtestBtn"
                            class="btn-warning w-full text-lg py-3"
                            onclick="backtestStock()"
                        >
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                            策略回测
                        </button>

                        <button
                            id="multiIndicatorsBtn"
                            class="btn-secondary w-full text-lg py-3"
                            onclick="analyzeMultiIndicators()"
                        >
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            多指标分析
                        </button>
                    </div>

                    <!-- 收藏功能 -->
                    <div class="text-center">
                        <div class="flex items-center justify-center gap-4 mb-4">
                            <button
                                id="favoriteBtn"
                                class="btn-favorite"
                                onclick="toggleFavorite()"
                                title="收藏当前股票"
                            >
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                收藏股票
                            </button>
                            <button
                                id="manageFavoritesBtn"
                                class="btn-manage-favorites"
                                onclick="toggleFavoritesPanel()"
                                title="管理收藏"
                            >
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                管理收藏
                            </button>
                        </div>

                        <!-- 收藏列表面板 -->
                        <div id="favoritesPanel" class="hidden">
                            <p class="text-sm text-gray-500 mb-3">我的收藏股票</p>
                            <div id="favoritesList" class="flex flex-wrap justify-center gap-2 mb-4">
                                <!-- 收藏的股票将在这里显示 -->
                            </div>
                            <div class="text-xs text-gray-600">
                                点击股票快速分析，右键删除收藏
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div id="analysisResult" class="hidden max-w-6xl mx-auto"></div>
    </main>

    <!-- 页脚 -->
    <footer class="border-t border-gray-800 mt-16">
        <div class="container mx-auto px-6 py-8">
            <div class="text-center text-gray-500">
                <p class="text-sm">© 2025 A股量化分析系统 · 专业投资分析工具</p>
                <p class="text-xs mt-2">数据仅供参考，投资有风险，入市需谨慎</p>
            </div>
        </div>
    </footer>

    <script>
        // 收藏功能相关变量
        let favorites = JSON.parse(localStorage.getItem('stockFavorites') || '[]');
        let currentStock = '';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateFavoritesList();
            updateFavoriteButton();
        });

        // 收藏/取消收藏股票
        function toggleFavorite() {
            const symbol = document.getElementById('stockSymbol').value.trim().toUpperCase();
            if (!symbol) {
                alert('请先输入股票代码');
                return;
            }

            currentStock = symbol;

            if (favorites.includes(symbol)) {
                // 取消收藏
                favorites = favorites.filter(s => s !== symbol);
                showNotification(`已取消收藏 ${symbol}`, 'info');
            } else {
                // 添加收藏
                favorites.push(symbol);
                showNotification(`已收藏 ${symbol}`, 'success');
            }

            // 保存到本地存储
            localStorage.setItem('stockFavorites', JSON.stringify(favorites));

            // 更新UI
            updateFavoriteButton();
            updateFavoritesList();
        }

        // 更新收藏按钮状态
        function updateFavoriteButton() {
            const symbol = document.getElementById('stockSymbol').value.trim().toUpperCase();
            const btn = document.getElementById('favoriteBtn');
            const svg = btn.querySelector('svg');

            if (favorites.includes(symbol)) {
                btn.classList.add('favorited');
                btn.innerHTML = `
                    <svg class="w-5 h-5 inline mr-2" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    已收藏
                `;
            } else {
                btn.classList.remove('favorited');
                btn.innerHTML = `
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    收藏股票
                `;
            }
        }

        // 更新收藏列表
        function updateFavoritesList() {
            const container = document.getElementById('favoritesList');

            if (favorites.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-sm">暂无收藏股票</div>';
                return;
            }

            container.innerHTML = favorites.map(symbol => `
                <button
                    class="favorite-stock-btn"
                    onclick="quickAnalyze('${symbol}')"
                    oncontextmenu="removeFavorite('${symbol}', event)"
                    title="左键分析，右键删除"
                >
                    ${symbol}
                </button>
            `).join('');
        }

        // 切换收藏面板显示
        function toggleFavoritesPanel() {
            const panel = document.getElementById('favoritesPanel');
            panel.classList.toggle('hidden');
        }

        // 删除收藏
        function removeFavorite(symbol, event) {
            event.preventDefault();
            event.stopPropagation();

            if (confirm(`确定要删除收藏的 ${symbol} 吗？`)) {
                favorites = favorites.filter(s => s !== symbol);
                localStorage.setItem('stockFavorites', JSON.stringify(favorites));
                updateFavoritesList();
                updateFavoriteButton();
                showNotification(`已删除收藏 ${symbol}`, 'info');
            }
        }

        // 快速分析收藏的股票
        function quickAnalyze(symbol) {
            document.getElementById('stockSymbol').value = symbol;
            updateFavoriteButton();

            // 添加分析中的视觉反馈
            const buttons = document.querySelectorAll('.favorite-stock-btn');
            buttons.forEach(btn => {
                if (btn.textContent.trim() === symbol) {
                    btn.classList.add('analyzing');
                    setTimeout(() => btn.classList.remove('analyzing'), 3000);
                }
            });

            analyzeStock();
        }

        // 监听输入框变化，更新收藏按钮状态
        document.addEventListener('DOMContentLoaded', function() {
            const input = document.getElementById('stockSymbol');
            input.addEventListener('input', updateFavoriteButton);
            input.addEventListener('change', updateFavoriteButton);
        });

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

            // 根据类型设置样式
            switch(type) {
                case 'success':
                    notification.className += ' bg-green-600 text-white';
                    break;
                case 'error':
                    notification.className += ' bg-red-600 text-white';
                    break;
                case 'info':
                default:
                    notification.className += ' bg-blue-600 text-white';
                    break;
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // AI分析
        async function analyzeStock() {
            const symbol = document.getElementById('stockSymbol').value.trim();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const btn = document.getElementById('analyzeBtn');
            const originalText = btn.innerHTML;

            // 显示加载状态
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div> 分析中...';

            try {
                console.log('开始AI分析:', symbol);
                const response = await axios.get(`http://localhost:8000/api/analyze/${symbol.toUpperCase()}`);
                console.log('AI分析响应:', response.status, response.data);

                if (response.status === 200 && response.data) {
                    displayAnalysisResult(response.data);
                } else {
                    throw new Error(`API返回状态: ${response.status}`);
                }
            } catch (error) {
                console.error('AI分析失败:', error);
                console.error('错误详情:', error.response?.data || error.message);

                let errorMessage = 'AI分析失败，请检查股票代码是否正确或稍后重试';
                if (error.response?.status === 404) {
                    errorMessage = '未找到该股票代码的数据';
                } else if (error.response?.status === 500) {
                    errorMessage = '服务器内部错误，请稍后重试';
                }

                alert(errorMessage);
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 走势预测
        async function predictStock() {
            const symbol = document.getElementById('stockSymbol').value.trim();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const btn = document.getElementById('predictBtn');
            const originalText = btn.innerHTML;

            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div> 预测中...';

            try {
                const response = await axios.get(`http://localhost:8000/api/predict/${symbol.toUpperCase()}`);
                if (response.status === 200 && response.data) {
                    displayPredictionResult(response.data);
                } else {
                    throw new Error(`API返回状态: ${response.status}`);
                }
            } catch (error) {
                console.error('走势预测失败:', error);
                alert('走势预测失败，请稍后重试');
            } finally {
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 策略回测
        async function backtestStock() {
            const symbol = document.getElementById('stockSymbol').value.trim();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const btn = document.getElementById('backtestBtn');
            const originalText = btn.innerHTML;

            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div> 回测中...';

            try {
                const response = await axios.get(`http://localhost:8000/api/backtest/${symbol.toUpperCase()}/ma_crossover`);
                if (response.status === 200 && response.data) {
                    displayBacktestResult(response.data);
                } else {
                    throw new Error(`API返回状态: ${response.status}`);
                }
            } catch (error) {
                console.error('策略回测失败:', error);
                alert('策略回测失败，请稍后重试');
            } finally {
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 多指标分析
        async function analyzeMultiIndicators() {
            const symbol = document.getElementById('stockSymbol').value.trim();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const btn = document.getElementById('multiIndicatorsBtn');
            const originalText = btn.innerHTML;

            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div> 分析中...';

            try {
                const response = await axios.get(`http://localhost:8000/api/multi-indicators/${symbol.toUpperCase()}`);
                if (response.status === 200 && response.data) {
                    displayMultiIndicatorsResult(response.data);
                } else {
                    throw new Error(`API返回状态: ${response.status}`);
                }
            } catch (error) {
                console.error('多指标分析失败:', error);
                alert('多指标分析失败，请稍后重试');
            } finally {
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 显示AI分析结果
        function displayAnalysisResult(data) {
            const resultDiv = document.getElementById('analysisResult');

            resultDiv.innerHTML = `
                <div class="card animate-fade-in">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-2xl font-bold gradient-text">${data.symbol} AI分析报告</h3>
                        <div class="text-sm text-gray-400">${new Date().toLocaleString()}</div>
                    </div>

                    <!-- 股票基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-blue-400">¥${data.current_price ? data.current_price.toFixed(2) : 'N/A'}</div>
                            <div class="text-sm text-gray-400">当前价格</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold ${(data.price_change || 0) >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${(data.price_change || 0) >= 0 ? '+' : ''}${(data.price_change || 0).toFixed(2)}
                            </div>
                            <div class="text-sm text-gray-400">价格变动</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold ${(data.price_change_percent || 0) >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${(data.price_change_percent || 0) >= 0 ? '+' : ''}${(data.price_change_percent || 0).toFixed(2)}%
                            </div>
                            <div class="text-sm text-gray-400">涨跌幅</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-purple-400">${data.price_data ? data.price_data.length : (data.technical_indicators ? Object.keys(data.technical_indicators).length : 'N/A')}</div>
                            <div class="text-sm text-gray-400">数据点数</div>
                        </div>
                    </div>

                    <!-- AI分析结果 -->
                    <div class="space-y-6">
                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-blue-400">🤖 AI智能分析</h4>
                            <p class="text-gray-300 leading-relaxed">${data.summary || '暂无分析数据'}</p>
                        </div>

                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-green-400">💡 投资建议</h4>
                            <div class="text-gray-300 leading-relaxed">
                                <div class="mb-4 p-4 rounded-lg ${getRecommendationStyle(data.recommendation)}">
                                    <div class="flex items-center mb-2">
                                        <span class="text-2xl mr-2">${getRecommendationIcon(data.recommendation)}</span>
                                        <strong class="text-lg">${getRecommendationText(data.recommendation)}</strong>
                                    </div>
                                    <p class="text-sm opacity-90">${getRecommendationDescription(data.recommendation)}</p>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div><strong>AI信心指数:</strong> ${data.confidence ? (data.confidence * 100).toFixed(1) + '%' : 'N/A'}</div>
                                    <div><strong>目标价位:</strong> ${data.target_price ? '¥' + data.target_price.toFixed(2) : 'N/A'}</div>
                                    <div><strong>预期收益:</strong> ${data.expected_return ? (data.expected_return * 100).toFixed(1) + '%' : 'N/A'}</div>
                                    <div><strong>建议持有期:</strong> ${getTimeHorizonText(data.time_horizon)}</div>
                                </div>
                            </div>
                        </div>

                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-yellow-400">⚠️ 风险提示</h4>
                            <div class="text-gray-300 leading-relaxed">
                                ${data.risk_warnings && data.risk_warnings.length > 0 ?
                                    data.risk_warnings.map(warning => `<p>• ${warning}</p>`).join('') :
                                    '<p>暂无特殊风险提示</p>'
                                }
                            </div>
                        </div>

                        ${data.key_signals && data.key_signals.length > 0 ? `
                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-purple-400">📊 关键信号</h4>
                            <div class="text-gray-300 leading-relaxed">
                                ${data.key_signals.map(signal => `<p>• ${signal}</p>`).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;

            resultDiv.classList.remove('hidden');
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示预测结果
        function displayPredictionResult(data) {
            const resultDiv = document.getElementById('analysisResult');

            // 获取最新预测数据
            const latestPrediction = data.predictions && data.predictions.length > 0 ? data.predictions[data.predictions.length - 1] : null;
            const predictedPrice = latestPrediction ? latestPrediction.predicted_price : data.current_price;
            const predictedChange = latestPrediction ? ((predictedPrice - data.current_price) / data.current_price * 100) : 0;

            resultDiv.innerHTML = `
                <div class="card animate-fade-in">
                    <h3 class="text-2xl font-bold gradient-text mb-6">${data.symbol} 走势预测</h3>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-blue-400">¥${data.current_price.toFixed(2)}</div>
                            <div class="text-sm text-gray-400">当前价格</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-green-400">¥${predictedPrice.toFixed(2)}</div>
                            <div class="text-sm text-gray-400">预测价格</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold ${predictedChange >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${predictedChange >= 0 ? '+' : ''}${predictedChange.toFixed(2)}%
                            </div>
                            <div class="text-sm text-gray-400">预期涨跌</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-purple-400">${(data.overall_confidence * 100).toFixed(1)}%</div>
                            <div class="text-sm text-gray-400">预测置信度</div>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-purple-400">📈 趋势预测</h4>
                            <div class="text-gray-300 leading-relaxed">
                                <p><strong>趋势方向:</strong> ${data.trend_direction || 'N/A'}</p>
                                <p><strong>趋势强度:</strong> ${data.trend_strength || 'N/A'}</p>
                                <p><strong>风险等级:</strong> ${data.risk_level || 'N/A'}</p>
                                <p><strong>预测周期:</strong> ${data.prediction_horizon || 'N/A'}</p>
                                <p><strong>使用模型:</strong> ${data.model_used || 'N/A'}</p>
                            </div>
                        </div>

                        ${data.predictions && data.predictions.length > 0 ? `
                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-blue-400">📊 详细预测</h4>
                            <div class="space-y-2">
                                ${data.predictions.slice(-5).map((pred, index) => `
                                    <div class="flex justify-between items-center p-2 bg-gray-800/50 rounded">
                                        <span class="text-gray-400">${pred.date || '第' + (index + 1) + '期'}</span>
                                        <span class="text-blue-400 font-medium">¥${pred.predicted_price.toFixed(2)}</span>
                                        <span class="text-gray-400">${pred.confidence ? (pred.confidence * 100).toFixed(1) + '%' : 'N/A'}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;

            resultDiv.classList.remove('hidden');
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示回测结果
        function displayBacktestResult(data) {
            const resultDiv = document.getElementById('analysisResult');

            const performance = data.performance || {};
            const riskAnalysis = data.risk_analysis || {};

            resultDiv.innerHTML = `
                <div class="card animate-fade-in">
                    <h3 class="text-2xl font-bold gradient-text mb-6">${data.symbol} 策略回测</h3>

                    <div class="mb-6 p-4 bg-gray-900/50 rounded-lg border border-gray-700">
                        <h4 class="font-bold text-lg mb-2 text-blue-400">📋 策略信息</h4>
                        <p class="text-gray-300"><strong>策略名称:</strong> ${data.strategy || 'N/A'}</p>
                        <p class="text-gray-300"><strong>回测周期:</strong> ${data.period || 'N/A'}</p>
                        <p class="text-gray-300 mt-2">${data.strategy_description || '暂无策略描述'}</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                        <div class="metric-card">
                            <div class="text-2xl font-bold ${performance.total_return >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${performance.total_return >= 0 ? '+' : ''}${(performance.total_return * 100).toFixed(2)}%
                            </div>
                            <div class="text-sm text-gray-400">总收益率</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-blue-400">${data.trades ? data.trades.length : 0}</div>
                            <div class="text-sm text-gray-400">交易次数</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-green-400">${performance.win_rate !== undefined ? (performance.win_rate * 100).toFixed(1) : 'N/A'}%</div>
                            <div class="text-sm text-gray-400">胜率</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold ${(performance.max_drawdown || 0) >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${performance.max_drawdown !== undefined ? (performance.max_drawdown * 100).toFixed(2) : 'N/A'}%
                            </div>
                            <div class="text-sm text-gray-400">最大回撤</div>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-orange-400">📊 绩效指标</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-300">
                                <div><strong>年化收益率:</strong> ${performance.annual_return !== undefined ? (performance.annual_return * 100).toFixed(2) + '%' : 'N/A'}</div>
                                <div><strong>夏普比率:</strong> ${performance.sharpe_ratio !== undefined ? performance.sharpe_ratio.toFixed(3) : 'N/A'}</div>
                                <div><strong>盈利因子:</strong> ${performance.profit_factor !== undefined ? performance.profit_factor.toFixed(2) : 'N/A'}</div>
                                <div><strong>平均盈利:</strong> ${performance.avg_win !== undefined ? '¥' + performance.avg_win.toFixed(2) : 'N/A'}</div>
                                <div><strong>平均亏损:</strong> ${performance.avg_loss !== undefined ? '¥' + performance.avg_loss.toFixed(2) : 'N/A'}</div>
                                <div><strong>盈利交易:</strong> ${performance.winning_trades !== undefined ? performance.winning_trades + '笔' : 'N/A'}</div>
                                <div><strong>亏损交易:</strong> ${performance.losing_trades !== undefined ? performance.losing_trades + '笔' : 'N/A'}</div>
                                <div><strong>总交易数:</strong> ${performance.total_trades !== undefined ? performance.total_trades + '笔' : 'N/A'}</div>
                            </div>
                        </div>

                        ${riskAnalysis && Object.keys(riskAnalysis).length > 0 ? `
                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-red-400">⚠️ 风险分析</h4>
                            <div class="text-gray-300 space-y-2">
                                ${Object.entries(riskAnalysis).map(([key, value]) => `
                                    <p><strong>${key}:</strong> ${value}</p>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${data.trades && data.trades.length > 0 ? `
                        <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                            <h4 class="font-bold text-lg mb-3 text-purple-400">📈 最近交易记录</h4>
                            <div class="space-y-2 max-h-60 overflow-y-auto scrollbar-hide">
                                ${data.trades.slice(-10).map(trade => `
                                    <div class="flex justify-between items-center p-2 bg-gray-800/50 rounded">
                                        <span class="text-gray-400">${trade.date || 'N/A'}</span>
                                        <span class="${trade.action === 'BUY' ? 'text-green-400' : 'text-red-400'}">${trade.action || 'N/A'}</span>
                                        <span class="text-yellow-400">¥${trade.price ? trade.price.toFixed(2) : 'N/A'}</span>
                                        <span class="text-gray-400">${trade.quantity || 'N/A'}股</span>
                                        <span class="text-blue-400">
                                            ¥${trade.commission ? trade.commission.toFixed(2) : 'N/A'}
                                        </span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;

            resultDiv.classList.remove('hidden');
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示多指标分析结果
        function displayMultiIndicatorsResult(data) {
            const resultDiv = document.getElementById('analysisResult');

            // 生成指标卡片HTML
            const indicatorsHtml = Object.entries(data.detailed_indicators).map(([key, indicator]) => {
                const signalClass = getSignalClass(indicator.signal);
                const strengthClass = getStrengthClass(indicator.strength);

                return `
                    <div class="card">
                        <div class="flex justify-between items-start mb-4">
                            <h4 class="font-bold text-lg">${indicator.name}</h4>
                            <div class="flex gap-2">
                                <span class="signal-badge ${signalClass}">
                                    ${getSignalText(indicator.signal)}
                                </span>
                                <span class="signal-badge ${strengthClass}">
                                    ${getStrengthText(indicator.strength)}
                                </span>
                            </div>
                        </div>

                        ${generateIndicatorDetails(key, indicator)}

                        <div class="mt-4 p-4 bg-gray-900/50 rounded-lg">
                            <p class="text-sm text-gray-300">${indicator.description}</p>
                            ${indicator.interpretation ? `<p class="text-sm text-blue-400 mt-2">${indicator.interpretation}</p>` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            resultDiv.innerHTML = `
                <div class="card animate-fade-in">
                    <h3 class="text-2xl font-bold gradient-text mb-6">${data.symbol} 多指标分析报告</h3>

                    <!-- 股票基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-blue-400">¥${data.current_price ? data.current_price.toFixed(2) : 'N/A'}</div>
                            <div class="text-sm text-gray-400">当前价格</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold ${(data.price_change || 0) >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${(data.price_change || 0) >= 0 ? '+' : ''}${(data.price_change || 0).toFixed(2)}
                            </div>
                            <div class="text-sm text-gray-400">价格变动</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold ${(data.price_change_percent || 0) >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${(data.price_change_percent || 0) >= 0 ? '+' : ''}${(data.price_change_percent || 0).toFixed(2)}%
                            </div>
                            <div class="text-sm text-gray-400">涨跌幅</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-purple-400">${data.data_points || Object.keys(data.detailed_indicators || {}).length || 'N/A'}</div>
                            <div class="text-sm text-gray-400">数据点数</div>
                        </div>
                    </div>

                    <!-- 综合评分 -->
                    <div class="card mb-8" style="background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));">
                        <h4 class="font-bold text-xl mb-6 text-center gradient-text">🎯 综合评分</h4>
                        <div class="text-center mb-6">
                            <div class="text-5xl font-bold ${getScoreColor(data.overall_score.overall_score)} mb-2">${data.overall_score.overall_score}</div>
                            <div class="text-lg text-gray-400">分 (满分100分)</div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="metric-card">
                                <div class="font-bold text-lg ${getSignalClass(data.overall_score.overall_signal)}">
                                    ${getSignalText(data.overall_score.overall_signal)}
                                </div>
                                <div class="text-sm text-gray-400">综合信号</div>
                            </div>
                            <div class="metric-card">
                                <div class="font-bold text-lg">${data.overall_score.overall_strength}</div>
                                <div class="text-sm text-gray-400">信号强度</div>
                            </div>
                            <div class="metric-card">
                                <div class="font-bold text-lg">${data.overall_score.confidence_level}</div>
                                <div class="text-sm text-gray-400">置信水平</div>
                            </div>
                        </div>

                        <div class="p-4 bg-gray-900/50 rounded-lg">
                            <p class="text-gray-300 text-center">${data.overall_score.interpretation}</p>
                        </div>
                    </div>

                    <!-- 信号分布 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                        <div class="metric-card" style="border-color: var(--success);">
                            <div class="text-2xl font-bold text-green-400">${data.overall_score.signal_distribution.BUY}</div>
                            <div class="text-sm text-gray-400">看涨信号</div>
                        </div>
                        <div class="metric-card">
                            <div class="text-2xl font-bold text-gray-400">${data.overall_score.signal_distribution.NEUTRAL}</div>
                            <div class="text-sm text-gray-400">中性信号</div>
                        </div>
                        <div class="metric-card" style="border-color: var(--danger);">
                            <div class="text-2xl font-bold text-red-400">${data.overall_score.signal_distribution.SELL}</div>
                            <div class="text-sm text-gray-400">看跌信号</div>
                        </div>
                    </div>

                    <!-- 详细指标分析 -->
                    <div class="mb-8">
                        <h4 class="font-bold text-xl mb-6 gradient-text">📊 详细指标分析</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            ${indicatorsHtml}
                        </div>
                    </div>

                    <!-- 分析说明 -->
                    <div class="p-6 bg-gray-900/50 rounded-lg border border-gray-700">
                        <h4 class="font-bold text-lg mb-3 text-gray-300">📚 分析说明</h4>
                        <ul class="text-sm text-gray-400 space-y-2">
                            <li>• 多指标分析基于RSI、MACD、移动平均、布林带、成交量、动量等技术指标</li>
                            <li>• 综合评分范围为-100到+100分，分数越高表示看涨信号越强</li>
                            <li>• 信号强度分为强势(STRONG)、中等(MODERATE)、弱势(WEAK)三个级别</li>
                            <li>• 建议结合基本面分析和市场环境综合判断</li>
                            <li>• 数据更新时间: ${new Date().toLocaleString()}</li>
                        </ul>
                    </div>
                </div>
            `;

            resultDiv.classList.remove('hidden');
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 辅助函数
        function getSignalClass(signal) {
            switch(signal) {
                case 'BUY': return 'signal-buy';
                case 'SELL': return 'signal-sell';
                default: return 'signal-neutral';
            }
        }

        function getSignalText(signal) {
            switch(signal) {
                case 'BUY': return '看涨';
                case 'SELL': return '看跌';
                default: return '中性';
            }
        }

        function getStrengthClass(strength) {
            switch(strength) {
                case 'STRONG': return 'strength-strong';
                case 'MODERATE': return 'strength-moderate';
                default: return 'strength-weak';
            }
        }

        function getStrengthText(strength) {
            switch(strength) {
                case 'STRONG': return '强';
                case 'MODERATE': return '中';
                default: return '弱';
            }
        }

        function getScoreColor(score) {
            if (score > 60) return 'text-green-400';
            if (score > 30) return 'text-blue-400';
            if (score > -30) return 'text-gray-400';
            if (score > -60) return 'text-orange-400';
            return 'text-red-400';
        }

        function generateIndicatorDetails(key, indicator) {
            switch(key) {
                case 'rsi':
                    return `
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>当前值: <span class="font-medium text-blue-400">${indicator.current_value}</span></div>
                            <div>变化: <span class="font-medium ${indicator.change >= 0 ? 'text-green-400' : 'text-red-400'}">${indicator.change >= 0 ? '+' : ''}${indicator.change}</span></div>
                            <div>水平: <span class="font-medium text-purple-400">${indicator.level}</span></div>
                            <div>历史均值: <span class="font-medium text-gray-400">${indicator.historical_range?.avg?.toFixed(1) || 'N/A'}</span></div>
                        </div>
                    `;
                case 'macd':
                    return `
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>MACD线: <span class="font-medium text-blue-400">${indicator.macd_line}</span></div>
                            <div>信号线: <span class="font-medium text-purple-400">${indicator.signal_line}</span></div>
                            <div>柱状图: <span class="font-medium text-yellow-400">${indicator.histogram}</span></div>
                            <div>趋势: <span class="font-medium text-green-400">${indicator.trend}</span></div>
                        </div>
                    `;
                case 'moving_averages':
                    return `
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>MA5: <span class="font-medium text-blue-400">¥${indicator.ma5}</span></div>
                            <div>MA20: <span class="font-medium text-purple-400">¥${indicator.ma20}</span></div>
                            <div>价格vs MA5: <span class="font-medium ${indicator.price_vs_ma5 >= 0 ? 'text-green-400' : 'text-red-400'}">${indicator.price_vs_ma5 >= 0 ? '+' : ''}${indicator.price_vs_ma5}%</span></div>
                            <div>趋势强度: <span class="font-medium text-yellow-400">${indicator.trend_strength}</span></div>
                        </div>
                    `;
                case 'bollinger_bands':
                    return `
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>上轨: <span class="font-medium text-red-400">¥${indicator.upper_band}</span></div>
                            <div>下轨: <span class="font-medium text-green-400">¥${indicator.lower_band}</span></div>
                            <div>位置: <span class="font-medium text-purple-400">${indicator.position}</span></div>
                            <div>带宽: <span class="font-medium text-blue-400">${indicator.width}%</span></div>
                        </div>
                    `;
                case 'volume':
                    return `
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>当前量: <span class="font-medium text-blue-400">${(indicator.current_volume / 10000).toFixed(1)}万</span></div>
                            <div>5日均量: <span class="font-medium text-purple-400">${(indicator.avg_volume_5 / 10000).toFixed(1)}万</span></div>
                            <div>量比: <span class="font-medium text-yellow-400">${indicator.volume_ratio}</span></div>
                            <div>趋势: <span class="font-medium text-green-400">${indicator.volume_trend}</span></div>
                        </div>
                    `;
                case 'momentum':
                    return `
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>5日ROC: <span class="font-medium ${indicator.roc_5 >= 0 ? 'text-green-400' : 'text-red-400'}">${indicator.roc_5 >= 0 ? '+' : ''}${indicator.roc_5}%</span></div>
                            <div>10日ROC: <span class="font-medium ${indicator.roc_10 >= 0 ? 'text-green-400' : 'text-red-400'}">${indicator.roc_10 >= 0 ? '+' : ''}${indicator.roc_10}%</span></div>
                            <div>20日ROC: <span class="font-medium ${indicator.roc_20 >= 0 ? 'text-green-400' : 'text-red-400'}">${indicator.roc_20 >= 0 ? '+' : ''}${indicator.roc_20}%</span></div>
                            <div>动量得分: <span class="font-medium text-purple-400">${indicator.momentum_score?.toFixed(1) || 'N/A'}</span></div>
                        </div>
                    `;
                case 'volatility':
                    return `
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>日波动率: <span class="font-medium text-blue-400">${(indicator.daily_volatility * 100).toFixed(2)}%</span></div>
                            <div>周波动率: <span class="font-medium text-purple-400">${(indicator.weekly_volatility * 100).toFixed(2)}%</span></div>
                            <div>月波动率: <span class="font-medium text-yellow-400">${(indicator.monthly_volatility * 100).toFixed(2)}%</span></div>
                            <div>波动水平: <span class="font-medium text-green-400">${indicator.volatility_level}</span></div>
                        </div>
                    `;
                default:
                    return '<div class="text-sm text-gray-500">详细数据不可用</div>';
            }
        }

        // 回车键触发分析
        document.getElementById('stockSymbol').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeStock();
            }
        });

        // 将专业术语转换为小白用户容易理解的表述
        function getRecommendationText(recommendation) {
            const recommendations = {
                'BUY': '建议买入',
                'STRONG_BUY': '强烈建议买入',
                'HOLD': '建议持有',
                'SELL': '建议卖出',
                'STRONG_SELL': '强烈建议卖出'
            };
            return recommendations[recommendation] || '暂无建议';
        }

        function getRecommendationIcon(recommendation) {
            const icons = {
                'BUY': '🚀',
                'STRONG_BUY': '💎',
                'HOLD': '🤝',
                'SELL': '📉',
                'STRONG_SELL': '⚠️'
            };
            return icons[recommendation] || '❓';
        }

        function getRecommendationDescription(recommendation) {
            const descriptions = {
                'BUY': '根据技术分析，该股票具有上涨潜力，适合买入建仓',
                'STRONG_BUY': '多项指标显示强烈买入信号，建议积极配置',
                'HOLD': '当前价位相对合理，建议继续持有，等待更好时机',
                'SELL': '技术指标显示下跌风险，建议适当减仓或卖出',
                'STRONG_SELL': '多项指标显示强烈卖出信号，建议尽快减仓'
            };
            return descriptions[recommendation] || '请结合市场情况和个人风险承受能力做出投资决策';
        }

        function getRecommendationStyle(recommendation) {
            const styles = {
                'BUY': 'bg-green-600/20 border border-green-500/30',
                'STRONG_BUY': 'bg-emerald-600/20 border border-emerald-500/30',
                'HOLD': 'bg-blue-600/20 border border-blue-500/30',
                'SELL': 'bg-orange-600/20 border border-orange-500/30',
                'STRONG_SELL': 'bg-red-600/20 border border-red-500/30'
            };
            return styles[recommendation] || 'bg-gray-600/20 border border-gray-500/30';
        }

        function getTimeHorizonText(timeHorizon) {
            if (!timeHorizon || timeHorizon === 'N/A') {
                return '根据市场情况';
            }

            // 如果是数字，转换为天数描述
            if (typeof timeHorizon === 'number') {
                if (timeHorizon <= 7) {
                    return `${timeHorizon}天 (短期)`;
                } else if (timeHorizon <= 30) {
                    return `${timeHorizon}天 (中短期)`;
                } else if (timeHorizon <= 90) {
                    return `${timeHorizon}天 (中期)`;
                } else {
                    return `${timeHorizon}天 (长期)`;
                }
            }

            // 如果是字符串，直接返回
            return timeHorizon;
        }
    </script>
</body>
</html>
