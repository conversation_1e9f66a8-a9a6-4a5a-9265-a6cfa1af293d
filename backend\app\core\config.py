from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    """应用配置"""

    # 基础配置
    APP_NAME: str = "智能量化分析系统"
    VERSION: str = "1.0.0"
    DEBUG: bool = True

    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # CORS配置
    ALLOWED_HOSTS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ]

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./quant_analysis.db"

    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"

    # API密钥
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 外部API配置
    ALPHA_VANTAGE_API_KEY: str = ""
    FINNHUB_API_KEY: str = ""

    # 机器学习配置
    MODEL_CACHE_DIR: str = "./models"
    DATA_CACHE_DIR: str = "./data"

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # 忽略额外的环境变量

# 创建设置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(settings.MODEL_CACHE_DIR, exist_ok=True)
os.makedirs(settings.DATA_CACHE_DIR, exist_ok=True)
os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True)
