#!/usr/bin/env python3
"""
验证真实数据
"""

import requests
import json

def verify_real_data():
    """验证是否使用真实数据"""
    print("🔍 验证A股真实数据")
    print("="*40)
    
    try:
        response = requests.get("http://localhost:8000/api/analyze/000001", timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ API响应成功")
            print(f"📈 股票名称: {data.get('company_name', 'N/A')}")
            print(f"💰 当前价格: ¥{data.get('current_price', 0):.2f}")
            print(f"📊 价格变化: {data.get('price_change', 0):+.2f} ({data.get('price_change_percent', 0):+.2f}%)")
            print(f"🔗 数据源: {data.get('data_source', '未知')}")
            print(f"⏰ 时间戳: {data.get('timestamp', 'N/A')}")
            
            # 检查是否是真实数据
            data_source = data.get('data_source', '')
            if 'tushare' in data_source.lower():
                print(f"🎉 使用Tushare真实数据！")
                return True
            else:
                print(f"⚠️ 可能使用模拟数据")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == '__main__':
    verify_real_data()
