from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any
import logging

from app.services.a_stock_analyzer import AStockAnalyzer
from app.services.ai_analyzer import AIAnalyzer
from app.models.analysis import AnalysisResponse
from app.utils.cache import cache_result

router = APIRouter()
logger = logging.getLogger(__name__)

# 初始化分析器
stock_analyzer = AStockAnalyzer()
ai_analyzer = AIAnalyzer()

@router.get("/analyze/{symbol}", response_model=AnalysisResponse)
async def analyze_stock(symbol: str, background_tasks: BackgroundTasks):
    """
    🤖 AI智能分析

    一键分析股票，获得专业投资建议
    """
    try:
        symbol = symbol.upper()
        logger.info(f"开始分析股票: {symbol}")

        # 检查缓存
        cache_key = f"analysis:{symbol}"
        cached_result = await cache_result.get(cache_key)
        if cached_result:
            logger.info(f"返回缓存结果: {symbol}")
            return cached_result

        # 获取股票数据
        stock_data = await stock_analyzer.get_stock_data(symbol)
        if not stock_data:
            raise HTTPException(
                status_code=404,
                detail=f"未找到股票 {symbol} 的数据，请检查股票代码是否正确"
            )

        # 计算技术指标
        technical_indicators = await stock_analyzer.calculate_indicators(stock_data)

        # AI分析
        ai_analysis = await ai_analyzer.analyze_stock(symbol, stock_data, technical_indicators)

        # 构建响应
        result = AnalysisResponse(
            symbol=symbol,
            company_name=stock_data.get('company_name', symbol),
            current_price=stock_data['current_price'],
            price_change=stock_data['price_change'],
            price_change_percent=stock_data['price_change_percent'],
            recommendation=ai_analysis['recommendation'],
            confidence=ai_analysis['confidence'],
            summary=ai_analysis['summary'],
            key_signals=ai_analysis['key_signals'],
            risk_warnings=ai_analysis['risk_warnings'],
            target_price=ai_analysis['target_price'],
            expected_return=ai_analysis['expected_return'],
            time_horizon=ai_analysis['time_horizon'],
            technical_indicators=technical_indicators,
            price_data=stock_data['price_history']
        )

        # 缓存结果 (5分钟)
        background_tasks.add_task(cache_result.set, cache_key, result, 300)

        logger.info(f"分析完成: {symbol} - {ai_analysis['recommendation']}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析失败 {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="分析服务暂时不可用，请稍后重试"
        )

@router.get("/indicators/{symbol}")
async def get_technical_indicators(symbol: str):
    """
    📊 获取技术指标

    获取股票的详细技术指标数据
    """
    try:
        symbol = symbol.upper()
        logger.info(f"获取技术指标: {symbol}")

        # 获取股票数据
        stock_data = await stock_analyzer.get_stock_data(symbol)
        if not stock_data:
            raise HTTPException(
                status_code=404,
                detail=f"未找到股票 {symbol} 的数据"
            )

        # 计算技术指标
        indicators = await stock_analyzer.calculate_indicators(stock_data)

        return {
            "symbol": symbol,
            "indicators": indicators,
            "timestamp": stock_data.get('timestamp')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取指标失败 {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="指标服务暂时不可用，请稍后重试"
        )

@router.get("/summary/{symbol}")
async def get_analysis_summary(symbol: str):
    """
    💡 获取分析摘要

    获取股票的简要分析摘要
    """
    try:
        symbol = symbol.upper()

        # 获取基础数据
        stock_data = await stock_analyzer.get_stock_data(symbol)
        if not stock_data:
            raise HTTPException(
                status_code=404,
                detail=f"未找到股票 {symbol} 的数据"
            )

        # 生成简要摘要
        summary = await ai_analyzer.generate_summary(symbol, stock_data)

        return {
            "symbol": symbol,
            "summary": summary,
            "current_price": stock_data['current_price'],
            "price_change_percent": stock_data['price_change_percent']
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取摘要失败 {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="摘要服务暂时不可用，请稍后重试"
        )
