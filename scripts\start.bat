@echo off
chcp 65001 >nul

echo 🚀 启动智能量化分析系统...

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)
echo ✅ Node.js 已安装

REM 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装，请先安装 Python
    pause
    exit /b 1
)
echo ✅ Python 已安装

REM 创建必要的目录
if not exist logs mkdir logs
if not exist data mkdir data
if not exist models mkdir models

REM 复制环境配置文件
if not exist .env (
    copy .env.example .env
    echo 📝 已创建 .env 文件，请根据需要修改配置
)

REM 安装前端依赖
echo 📦 安装前端依赖...
cd frontend
call npm install
cd ..

REM 安装后端依赖
echo 📦 安装后端依赖...
cd backend
pip install -r requirements.txt
cd ..

REM 启动后端服务
echo 🔧 启动后端服务...
cd backend
start "Backend" python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
cd ..

REM 等待后端启动
timeout /t 5 /nobreak >nul

REM 启动前端服务
echo 🎨 启动前端服务...
cd frontend
start "Frontend" npm run dev
cd ..

echo ✅ 系统启动完成！
echo.
echo 📱 前端地址: http://localhost:3000
echo 🔧 后端地址: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo.
echo 按任意键退出...
pause >nul
