# 依赖
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log

# 数据库
*.db
*.sqlite
*.sqlite3

# 缓存
.cache/
.parcel-cache/
.next/
.nuxt/
dist/
build/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 模型文件
models/
*.pkl
*.joblib
*.h5
*.pb

# 数据文件
data/
*.csv
*.json
*.parquet

# 临时文件
tmp/
temp/
.tmp/

# 测试覆盖率
coverage/
.nyc_output/
.coverage
htmlcov/

# 打包文件
*.tgz
*.tar.gz
*.zip

# 本地配置
local_settings.py
config.local.js
