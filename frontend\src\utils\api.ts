import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // 统一错误处理
    if (error.response?.status === 404) {
      console.error('API endpoint not found:', error.config?.url)
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error.response?.data?.detail || error.message)
    }
    return Promise.reject(error)
  }
)

// API方法
export const analysisAPI = {
  // 分析股票
  analyzeStock: (symbol: string) => 
    api.get(`/analyze/${symbol}`),
  
  // 获取技术指标
  getIndicators: (symbol: string) => 
    api.get(`/indicators/${symbol}`),
  
  // 获取分析摘要
  getSummary: (symbol: string) => 
    api.get(`/summary/${symbol}`),
}

export const predictionAPI = {
  // 预测股价
  predictPrice: (symbol: string, days: number = 30) => 
    api.get(`/predict/${symbol}?days=${days}`),
  
  // 自定义预测
  predictWithConfig: (data: any) => 
    api.post('/predict', data),
  
  // 获取可用模型
  getModels: () => 
    api.get('/models'),
  
  // 获取置信度
  getConfidence: (symbol: string) => 
    api.get(`/confidence/${symbol}`),
}

export const backtestAPI = {
  // 运行回测
  runBacktest: (data: any) => 
    api.post('/backtest', data),
  
  // 快速回测
  quickBacktest: (symbol: string, strategy: string) => 
    api.get(`/backtest/${symbol}/${strategy}`),
  
  // 获取可用策略
  getStrategies: () => 
    api.get('/strategies'),
  
  // 获取策略表现
  getPerformance: (symbol: string) => 
    api.get(`/performance/${symbol}`),
}

export default api
