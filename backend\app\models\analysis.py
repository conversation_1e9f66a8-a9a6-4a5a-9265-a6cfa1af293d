from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

class TechnicalIndicator(BaseModel):
    """技术指标模型"""
    name: str
    value: float
    signal: str = Field(..., description="信号类型: BUY, SELL, NEUTRAL")
    strength: str = Field(..., description="信号强度: STRONG, MODERATE, WEAK")
    description: str

class PriceData(BaseModel):
    """价格数据模型"""
    date: str
    price: float
    volume: int

class AnalysisResponse(BaseModel):
    """分析响应模型"""
    symbol: str = Field(..., description="股票代码")
    company_name: str = Field(..., description="公司名称")
    current_price: float = Field(..., description="当前价格")
    price_change: float = Field(..., description="价格变化")
    price_change_percent: float = Field(..., description="价格变化百分比")
    recommendation: str = Field(..., description="投资建议: BUY, SELL, HOLD")
    confidence: float = Field(..., ge=0, le=1, description="置信度 (0-1)")
    summary: str = Field(..., description="AI分析摘要")
    key_signals: List[str] = Field(..., description="关键信号列表")
    risk_warnings: List[str] = Field(..., description="风险警告列表")
    target_price: float = Field(..., description="目标价格")
    expected_return: float = Field(..., description="预期收益率")
    time_horizon: int = Field(..., description="投资时间周期(天)")
    technical_indicators: Dict[str, TechnicalIndicator] = Field(..., description="技术指标")
    price_data: List[PriceData] = Field(..., description="历史价格数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "company_name": "Apple Inc.",
                "current_price": 150.25,
                "price_change": 2.15,
                "price_change_percent": 1.45,
                "recommendation": "BUY",
                "confidence": 0.85,
                "summary": "苹果公司技术指标显示强劲上涨潜力，建议买入。",
                "key_signals": [
                    "RSI突破50，显示买入信号",
                    "MACD金叉，上涨趋势确立",
                    "成交量放大，市场关注度提升"
                ],
                "risk_warnings": [
                    "注意大盘走势影响",
                    "建议设置止损位"
                ],
                "target_price": 165.50,
                "expected_return": 10.15,
                "time_horizon": 30,
                "technical_indicators": {},
                "price_data": []
            }
        }

class IndicatorSummary(BaseModel):
    """指标摘要模型"""
    symbol: str
    indicators: Dict[str, TechnicalIndicator]
    timestamp: Optional[str] = None

class QuickSummary(BaseModel):
    """快速摘要模型"""
    symbol: str
    summary: str
    current_price: float
    price_change_percent: float
