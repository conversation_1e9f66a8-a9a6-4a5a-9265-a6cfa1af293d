#!/usr/bin/env python3
"""
测试Tushare连接
"""

def test_tushare_import():
    """测试tushare导入"""
    print("🧪 测试Tushare连接")
    print("="*40)
    
    try:
        import tushare as ts
        print("✅ Tushare导入成功")
        print(f"   版本: {ts.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Tushare导入失败: {e}")
        return False

def test_tushare_token():
    """测试tushare token"""
    print("\n🔑 测试Tushare Token...")
    
    try:
        import tushare as ts
        import os
        
        # 检查环境变量
        token = os.getenv('TUSHARE_TOKEN')
        if token:
            print(f"✅ 找到环境变量TUSHARE_TOKEN: {token[:10]}...")
        else:
            print("⚠️ 未找到环境变量TUSHARE_TOKEN")
        
        # 尝试设置token并连接
        if token:
            ts.set_token(token)
            pro = ts.pro_api()
            print("✅ Tushare Pro API初始化成功")
            return pro
        else:
            print("⚠️ 无法初始化Tushare Pro API (缺少token)")
            return None
            
    except Exception as e:
        print(f"❌ Tushare连接失败: {e}")
        return None

def test_tushare_data(pro):
    """测试tushare数据获取"""
    if not pro:
        print("\n⚠️ 跳过数据测试 (无有效连接)")
        return False
        
    print("\n📊 测试数据获取...")
    
    try:
        # 测试获取股票基本信息
        print("   测试股票基本信息...")
        basic = pro.stock_basic(ts_code='000001.SZ', fields='ts_code,symbol,name,area,industry,market')
        if not basic.empty:
            stock_info = basic.iloc[0]
            print(f"   ✅ 获取成功: {stock_info['name']} ({stock_info['ts_code']})")
        else:
            print("   ❌ 未获取到股票基本信息")
            return False
        
        # 测试获取历史数据
        print("   测试历史数据...")
        hist = pro.daily(ts_code='000001.SZ', start_date='20240101', end_date='20240131')
        if not hist.empty:
            print(f"   ✅ 获取成功: {len(hist)} 条历史数据")
            print(f"   最新价格: {hist.iloc[0]['close']}")
        else:
            print("   ❌ 未获取到历史数据")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ 数据获取失败: {e}")
        return False

def test_free_api():
    """测试免费API"""
    print("\n🆓 测试免费API...")
    
    try:
        import tushare as ts
        
        # 测试免费接口
        data = ts.get_realtime_quotes('000001')
        if data is not None and not data.empty:
            print("✅ 免费API可用")
            print(f"   股票: {data.iloc[0]['name']}")
            print(f"   价格: {data.iloc[0]['price']}")
            return True
        else:
            print("❌ 免费API不可用")
            return False
            
    except Exception as e:
        print(f"❌ 免费API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 Tushare连接诊断")
    print("="*50)
    
    # 测试导入
    if not test_tushare_import():
        print("\n❌ 请先安装tushare: pip install tushare")
        return 1
    
    # 测试token
    pro = test_tushare_token()
    
    # 测试数据获取
    data_ok = test_tushare_data(pro)
    
    # 如果Pro API不可用，测试免费API
    if not data_ok:
        free_ok = test_free_api()
    else:
        free_ok = False
    
    # 总结
    print("\n" + "="*50)
    print("📋 Tushare连接诊断报告")
    print("="*50)
    
    if data_ok:
        print("✅ Tushare Pro API: 可用")
        print("   建议: 使用Pro API获取高质量数据")
    elif free_ok:
        print("⚠️ Tushare Pro API: 不可用")
        print("✅ Tushare免费API: 可用")
        print("   建议: 申请tushare token以获得更好的数据服务")
    else:
        print("❌ 所有Tushare API: 不可用")
        print("   建议: 检查网络连接或使用模拟数据")
    
    print("\n💡 获取Tushare Token:")
    print("   1. 访问 https://tushare.pro/")
    print("   2. 注册账号并获取token")
    print("   3. 设置环境变量: set TUSHARE_TOKEN=your_token")
    print("   4. 重启系统")
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
