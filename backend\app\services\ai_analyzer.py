import numpy as np
import pandas as pd
from typing import Dict, List, Any
import logging
from datetime import datetime
import random

logger = logging.getLogger(__name__)

class AIAnalyzer:
    """AI智能分析器"""

    def __init__(self):
        self.models_loaded = False
        self._load_models()

    def _load_models(self):
        """加载AI模型 (模拟)"""
        try:
            # 这里应该加载真实的机器学习模型
            # 目前使用规则引擎模拟AI分析
            self.models_loaded = True
            logger.info("AI模型加载完成")
        except Exception as e:
            logger.error(f"AI模型加载失败: {str(e)}")
            self.models_loaded = False

    async def analyze_stock(self, symbol: str, stock_data: Dict[str, Any], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """AI股票分析"""
        try:
            # 综合分析各项指标
            signals = self._analyze_signals(indicators)

            # 生成投资建议
            recommendation = self._generate_recommendation(signals)

            # 计算置信度
            confidence = self._calculate_confidence(signals)

            # 生成分析摘要
            summary = self._generate_summary(symbol, stock_data, recommendation, confidence)

            # 提取关键信号
            key_signals = self._extract_key_signals(indicators, signals)

            # 识别风险警告
            risk_warnings = self._identify_risks(stock_data, indicators)

            # 计算目标价格
            target_price = self._calculate_target_price(stock_data, recommendation)

            # 计算预期收益
            expected_return = ((target_price - stock_data['current_price']) / stock_data['current_price']) * 100

            # 投资时间周期
            time_horizon = self._determine_time_horizon(recommendation, confidence)

            return {
                'recommendation': recommendation,
                'confidence': confidence,
                'summary': summary,
                'key_signals': key_signals,
                'risk_warnings': risk_warnings,
                'target_price': target_price,
                'expected_return': expected_return,
                'time_horizon': time_horizon
            }

        except Exception as e:
            logger.error(f"AI分析失败 {symbol}: {str(e)}")
            return self._get_default_analysis()

    def _analyze_signals(self, indicators: Dict[str, Any]) -> Dict[str, int]:
        """分析技术指标信号"""
        signals = {'buy': 0, 'sell': 0, 'neutral': 0}
        signal_weights = {
            'rsi': 1.2,
            'macd': 1.5,
            'ma': 1.3,
            'bollinger': 1.0,
            'volume': 0.8,
            'momentum': 1.1
        }

        for indicator_name, indicator_data in indicators.items():
            if indicator_name in signal_weights:
                weight = signal_weights[indicator_name]
                signal = indicator_data.get('signal', 'NEUTRAL')
                strength = indicator_data.get('strength', 'WEAK')

                # 根据强度调整权重
                strength_multiplier = {
                    'STRONG': 1.5,
                    'MODERATE': 1.0,
                    'WEAK': 0.5
                }.get(strength, 1.0)

                adjusted_weight = weight * strength_multiplier

                if signal == 'BUY':
                    signals['buy'] += adjusted_weight
                elif signal == 'SELL':
                    signals['sell'] += adjusted_weight
                else:
                    signals['neutral'] += adjusted_weight

        return signals

    def _generate_recommendation(self, signals: Dict[str, int]) -> str:
        """生成投资建议"""
        buy_score = signals['buy']
        sell_score = signals['sell']
        neutral_score = signals['neutral']

        total_score = buy_score + sell_score + neutral_score
        if total_score == 0:
            return 'HOLD'

        buy_ratio = buy_score / total_score
        sell_ratio = sell_score / total_score

        if buy_ratio > 0.6:
            return 'BUY'
        elif sell_ratio > 0.6:
            return 'SELL'
        else:
            return 'HOLD'

    def _calculate_confidence(self, signals: Dict[str, int]) -> float:
        """计算置信度"""
        total_score = sum(signals.values())
        if total_score == 0:
            return 0.5

        max_score = max(signals.values())
        confidence = max_score / total_score

        # 调整到合理范围
        return min(max(confidence, 0.3), 0.95)

    def _generate_summary(self, symbol: str, stock_data: Dict[str, Any], recommendation: str, confidence: float) -> str:
        """生成分析摘要"""
        price_change = stock_data['price_change_percent']
        company_name = stock_data.get('company_name', symbol)

        templates = {
            'BUY': [
                f"{company_name}({symbol})技术指标显示强劲上涨潜力，建议买入。多项指标支持看涨观点，预期短期内有良好表现。",
                f"基于技术分析，{company_name}处于上升趋势中，多项指标发出买入信号，建议积极配置。",
                f"{company_name}技术面表现良好，突破关键阻力位，建议买入并持有。"
            ],
            'SELL': [
                f"{company_name}({symbol})技术指标显示下跌风险增加，建议减仓或卖出以规避风险。",
                f"多项技术指标转为看跌，{company_name}可能面临调整压力，建议谨慎操作。",
                f"{company_name}跌破关键支撑位，技术面走弱，建议及时止损。"
            ],
            'HOLD': [
                f"{company_name}({symbol})目前处于震荡整理阶段，建议持有观望，等待明确方向。",
                f"技术指标信号混合，{company_name}短期方向不明确，建议保持现有仓位。",
                f"{company_name}在关键位置附近震荡，建议持有并密切关注突破方向。"
            ]
        }

        summary_list = templates.get(recommendation, templates['HOLD'])
        base_summary = random.choice(summary_list)

        # 添加置信度信息
        confidence_text = f"分析置信度为{confidence*100:.0f}%"

        return f"{base_summary} {confidence_text}。"

    def _extract_key_signals(self, indicators: Dict[str, Any], signals: Dict[str, int]) -> List[str]:
        """提取关键信号"""
        key_signals = []

        for indicator_name, indicator_data in indicators.items():
            signal = indicator_data.get('signal', 'NEUTRAL')
            strength = indicator_data.get('strength', 'WEAK')

            if strength in ['STRONG', 'MODERATE'] and signal != 'NEUTRAL':
                description = indicator_data.get('description', '')
                if description:
                    key_signals.append(description)

        # 如果没有强信号，添加一些通用信号
        if not key_signals:
            key_signals = [
                "技术指标处于正常波动范围",
                "市场情绪相对稳定",
                "成交量维持在合理水平"
            ]

        return key_signals[:5]  # 最多返回5个关键信号

    def _identify_risks(self, stock_data: Dict[str, Any], indicators: Dict[str, Any]) -> List[str]:
        """识别风险警告"""
        risks = []

        # 价格波动风险
        price_change = abs(stock_data['price_change_percent'])
        if price_change > 5:
            risks.append(f"价格波动较大({price_change:.1f}%)，注意风险控制")

        # RSI风险
        rsi_data = indicators.get('rsi', {})
        rsi_value = rsi_data.get('value', 50)
        if rsi_value > 80:
            risks.append("RSI显示严重超买，注意回调风险")
        elif rsi_value < 20:
            risks.append("RSI显示严重超卖，可能存在反弹机会")

        # 成交量风险
        volume_data = indicators.get('volume', {})
        if volume_data.get('signal') == 'NEUTRAL' and '萎缩' in volume_data.get('description', ''):
            risks.append("成交量萎缩，市场参与度不高")

        # 通用风险提示
        general_risks = [
            "市场存在不确定性，建议分散投资",
            "注意宏观经济环境变化的影响",
            "建议设置止损位控制风险"
        ]

        if not risks:
            risks.extend(general_risks[:2])
        else:
            risks.append(random.choice(general_risks))

        return risks[:4]  # 最多返回4个风险警告

    def _calculate_target_price(self, stock_data: Dict[str, Any], recommendation: str) -> float:
        """计算目标价格"""
        current_price = stock_data['current_price']

        # 基于推荐类型设置目标价格
        if recommendation == 'BUY':
            # 买入建议：目标价格上涨5-15%
            return current_price * (1 + random.uniform(0.05, 0.15))
        elif recommendation == 'SELL':
            # 卖出建议：目标价格下跌5-10%
            return current_price * (1 - random.uniform(0.05, 0.10))
        else:
            # 持有建议：目标价格小幅波动
            return current_price * (1 + random.uniform(-0.03, 0.05))

    def _determine_time_horizon(self, recommendation: str, confidence: float) -> int:
        """确定投资时间周期"""
        base_days = {
            'BUY': 30,
            'SELL': 14,
            'HOLD': 21
        }

        days = base_days.get(recommendation, 21)

        # 根据置信度调整时间周期
        if confidence > 0.8:
            days = int(days * 1.2)
        elif confidence < 0.5:
            days = int(days * 0.8)

        return days

    def _get_default_analysis(self) -> Dict[str, Any]:
        """获取默认分析结果"""
        return {
            'recommendation': 'HOLD',
            'confidence': 0.5,
            'summary': '暂时无法完成详细分析，建议谨慎操作。',
            'key_signals': ['技术分析暂时不可用'],
            'risk_warnings': ['建议等待更多数据后再做决策'],
            'target_price': 0,
            'expected_return': 0,
            'time_horizon': 30
        }

    async def generate_summary(self, symbol: str, stock_data: Dict[str, Any]) -> str:
        """生成简要摘要"""
        try:
            price_change = stock_data['price_change_percent']
            current_price = stock_data['current_price']

            if price_change > 2:
                trend = "强势上涨"
            elif price_change > 0:
                trend = "小幅上涨"
            elif price_change > -2:
                trend = "小幅下跌"
            else:
                trend = "明显下跌"

            return f"{symbol}当前价格${current_price:.2f}，今日{trend}{abs(price_change):.1f}%。技术面需要进一步观察。"

        except Exception as e:
            logger.error(f"生成摘要失败: {str(e)}")
            return f"{symbol}数据获取中，请稍后查看详细分析。"
