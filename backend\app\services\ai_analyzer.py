import numpy as np
import pandas as pd
from typing import Dict, List, Any
import logging
from datetime import datetime
import random
import os
import json
import httpx
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class DeepSeekResponse(BaseModel):
    """DeepSeek API响应模型"""
    recommendation: str
    confidence: float
    summary: str
    key_signals: List[str]
    risk_warnings: List[str]
    reasoning: str

class DeepSeekClient:
    """DeepSeek API客户端"""

    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        self.base_url = "https://api.deepseek.com/v1"
        self.model = "deepseek-chat"

        if not self.api_key:
            logger.warning("DeepSeek API密钥未配置，将使用规则引擎模式")

    async def generate_analysis(self, prompt: str) -> str:
        """调用DeepSeek API生成分析"""
        if not self.api_key:
            return None

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一位资深的A股投资分析师，具有20年的投资经验。请基于提供的技术指标数据，给出专业、客观的投资分析建议。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.7,
            "stream": False
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )

                if response.status_code == 200:
                    result = response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    logger.error(f"DeepSeek API错误: {response.status_code} - {response.text}")
                    return None

        except Exception as e:
            logger.error(f"调用DeepSeek API失败: {str(e)}")
            return None

class AIAnalyzer:
    """AI智能分析器 - 集成DeepSeek大模型"""

    def __init__(self):
        # 确保环境变量正确加载
        import os
        from dotenv import load_dotenv
        load_dotenv()

        self.deepseek_client = DeepSeekClient()
        self.models_loaded = False
        self._load_models()

        # 添加详细的调试信息
        api_key = os.getenv("DEEPSEEK_API_KEY")
        logger.info(f"🔍 AI分析器初始化")
        logger.info(f"   API密钥状态: {'✅ 已配置' if api_key else '❌ 未配置'}")
        if api_key:
            logger.info(f"   API密钥前缀: {api_key[:15]}...")
        logger.info(f"   使用模式: {'🤖 DeepSeek大模型' if self.use_llm else '🔧 规则引擎'}")

    def _load_models(self):
        """加载AI模型"""
        try:
            # 检查DeepSeek API是否可用
            if self.deepseek_client.api_key:
                logger.info("DeepSeek API已配置，使用大模型分析")
                self.use_llm = True
            else:
                logger.info("DeepSeek API未配置，使用规则引擎分析")
                self.use_llm = False

            self.models_loaded = True
            logger.info("AI分析器初始化完成")
        except Exception as e:
            logger.error(f"AI分析器初始化失败: {str(e)}")
            self.models_loaded = False
            self.use_llm = False

    async def analyze_stock(self, symbol: str, stock_data: Dict[str, Any], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """AI股票分析 - 支持DeepSeek大模型"""
        try:
            if self.use_llm:
                # 使用DeepSeek大模型分析
                return await self._analyze_with_deepseek(symbol, stock_data, indicators)
            else:
                # 使用规则引擎分析
                return await self._analyze_with_rules(symbol, stock_data, indicators)

        except Exception as e:
            logger.error(f"股票分析失败: {str(e)}")
            return self._get_default_analysis()

    async def _analyze_with_deepseek(self, symbol: str, stock_data: Dict[str, Any], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """使用DeepSeek大模型进行分析"""
        try:
            # 构建分析提示词
            prompt = self._build_analysis_prompt(symbol, stock_data, indicators)

            # 调用DeepSeek API
            llm_response = await self.deepseek_client.generate_analysis(prompt)

            if llm_response:
                # 解析大模型响应
                analysis_result = self._parse_llm_response(llm_response, stock_data)
                logger.info(f"DeepSeek分析完成: {symbol}")
                return analysis_result
            else:
                # 大模型调用失败，降级到规则引擎
                logger.warning("DeepSeek API调用失败，降级到规则引擎")
                return await self._analyze_with_rules(symbol, stock_data, indicators)

        except Exception as e:
            logger.error(f"DeepSeek分析失败: {str(e)}")
            return await self._analyze_with_rules(symbol, stock_data, indicators)

    async def _analyze_with_rules(self, symbol: str, stock_data: Dict[str, Any], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """使用规则引擎进行分析（备用方案）"""
        try:
            # 综合分析各项指标
            signals = self._analyze_signals(indicators)

            # 生成投资建议
            recommendation = self._generate_recommendation(signals)

            # 计算置信度
            confidence = self._calculate_confidence(signals)

            # 生成分析摘要
            summary = self._generate_summary(symbol, stock_data, recommendation, confidence)

            # 提取关键信号
            key_signals = self._extract_key_signals(indicators, signals)

            # 识别风险警告
            risk_warnings = self._identify_risks(stock_data, indicators)

            # 计算目标价格
            target_price = self._calculate_target_price(stock_data, recommendation)

            # 计算预期收益
            expected_return = ((target_price - stock_data['current_price']) / stock_data['current_price']) * 100

            # 投资时间周期
            time_horizon = self._determine_time_horizon(recommendation, confidence)

            return {
                'recommendation': recommendation,
                'confidence': confidence,
                'summary': summary,
                'key_signals': key_signals,
                'risk_warnings': risk_warnings,
                'target_price': target_price,
                'expected_return': expected_return,
                'time_horizon': time_horizon
            }

        except Exception as e:
            logger.error(f"规则引擎分析失败 {symbol}: {str(e)}")
            return self._get_default_analysis()

    def _build_analysis_prompt(self, symbol: str, stock_data: Dict[str, Any], indicators: Dict[str, Any]) -> str:
        """构建增强版DeepSeek分析提示词"""

        # 基础股票信息
        current_price = stock_data.get('current_price', 0)
        price_change = stock_data.get('price_change_percent', 0)
        company_name = stock_data.get('company_name', symbol)

        # 价格趋势分析
        price_data = stock_data.get('price_data', [])
        price_trend_analysis = self._analyze_price_trend(price_data, current_price)

        # 技术指标摘要
        indicators_summary = []
        for name, data in indicators.items():
            if isinstance(data, dict):
                signal = data.get('signal', 'NEUTRAL')
                strength = data.get('strength', 'WEAK')
                value = data.get('value', 'N/A')
                description = data.get('description', '')
                indicators_summary.append(f"- {name}: {signal}({strength}) 值:{value} {description}")

        indicators_text = "\n".join(indicators_summary) if indicators_summary else "技术指标数据不完整"

        prompt = f"""
请作为资深A股投资分析师，对以下股票进行专业分析：

【股票基本信息】
股票代码: {symbol}
公司名称: {company_name}
当前价格: ¥{current_price:.2f}
今日涨跌幅: {price_change:+.2f}%

【价格趋势分析】
{price_trend_analysis}

【技术指标分析】
{indicators_text}

【分析要求】
请基于以上多维度信息，提供专业的投资分析：

1. 综合技术面分析：
   - 结合多个技术指标的综合判断
   - 价格趋势和关键支撑阻力位分析
   - 成交量与价格关系的深度解读

2. 投资建议：从以下选项中选择一个
   - BUY (建议买入) - 技术面偏多，短期有上涨空间
   - STRONG_BUY (强烈建议买入) - 多项指标共振，强烈看多
   - HOLD (建议持有) - 技术面中性，等待更明确信号
   - SELL (建议卖出) - 技术面偏空，建议减仓
   - STRONG_SELL (强烈建议卖出) - 多项指标看空，建议清仓

3. 信心指数：60-95之间的数值，表示对建议的确信程度

4. 专业分析摘要：300字以内的深度分析，包括：
   - 当前技术面状态评估
   - 主要支撑和阻力位分析
   - 短期和中期趋势判断
   - 关键风险点和机会点

5. 关键技术信号：4-6个重要的技术信号点

6. 风险警示：3-5个主要风险因素

7. 目标价位：基于技术分析给出的合理目标价格

8. 预期收益率：相对当前价格的预期收益百分比

9. 建议持有期：投资时间周期建议（天数）

10. 操作策略：具体的买卖点位和仓位管理建议

请以JSON格式返回分析结果：
{{
    "recommendation": "投资建议",
    "confidence": 信心指数,
    "summary": "分析摘要",
    "key_signals": ["关键信号1", "关键信号2", "关键信号3"],
    "risk_warnings": ["风险提示1", "风险提示2"],
    "target_price": 目标价位,
    "expected_return": 预期收益率,
    "time_horizon": 建议持有期,
    "reasoning": "详细分析逻辑"
}}

注意：请确保分析客观、专业，基于技术指标给出合理建议。
"""

        return prompt

    def _analyze_price_trend(self, price_data: List[Dict], current_price: float) -> str:
        """分析价格趋势"""
        try:
            if not price_data or len(price_data) < 5:
                return "价格历史数据不足，无法进行趋势分析"

            # 提取价格数据
            prices = [float(item.get('price', 0)) for item in price_data[-60:]]  # 最近60天
            if not prices:
                return "价格数据无效"

            # 计算不同周期的涨跌幅
            changes = {}
            periods = [5, 10, 20, 30, 60]

            for period in periods:
                if len(prices) >= period:
                    old_price = prices[-period]
                    if old_price > 0:
                        change = ((current_price - old_price) / old_price) * 100
                        changes[f'{period}d'] = change

            # 计算波动率
            if len(prices) >= 20:
                import numpy as np
                returns = [((prices[i] - prices[i-1]) / prices[i-1]) for i in range(1, len(prices))]
                volatility = np.std(returns) * 100 * (252 ** 0.5)  # 年化波动率
            else:
                volatility = 0

            # 判断趋势方向
            if len(prices) >= 10:
                recent_trend = (prices[-1] - prices[-10]) / prices[-10] * 100
                if recent_trend > 5:
                    trend_direction = "强势上涨"
                elif recent_trend > 2:
                    trend_direction = "温和上涨"
                elif recent_trend > -2:
                    trend_direction = "横盘震荡"
                elif recent_trend > -5:
                    trend_direction = "温和下跌"
                else:
                    trend_direction = "明显下跌"
            else:
                trend_direction = "趋势不明"

            # 构建趋势分析文本
            trend_text = f"趋势方向: {trend_direction}\n"

            for period, change in changes.items():
                trend_text += f"近{period}涨跌幅: {change:+.2f}%\n"

            trend_text += f"价格波动率: {volatility:.2f}%\n"

            # 添加关键价位分析
            if len(prices) >= 20:
                max_price = max(prices[-20:])
                min_price = min(prices[-20:])
                trend_text += f"20日最高价: ¥{max_price:.2f}\n"
                trend_text += f"20日最低价: ¥{min_price:.2f}\n"
                trend_text += f"当前位置: {((current_price - min_price) / (max_price - min_price) * 100):.1f}%分位"

            return trend_text

        except Exception as e:
            logger.error(f"价格趋势分析失败: {str(e)}")
            return "价格趋势分析出现错误"

    def _parse_llm_response(self, llm_response: str, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析DeepSeek大模型响应"""
        try:
            # 尝试提取JSON部分
            import re

            # 查找JSON格式的响应
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    parsed_data = json.loads(json_str)

                    # 验证和标准化数据
                    result = {
                        'recommendation': self._validate_recommendation(parsed_data.get('recommendation', 'HOLD')),
                        'confidence': self._validate_confidence(parsed_data.get('confidence', 50)) / 100,
                        'summary': parsed_data.get('summary', '大模型分析完成，请查看详细指标。')[:500],
                        'key_signals': parsed_data.get('key_signals', ['技术分析完成'])[:5],
                        'risk_warnings': parsed_data.get('risk_warnings', ['请注意市场风险'])[:4],
                        'target_price': self._validate_target_price(
                            parsed_data.get('target_price', stock_data['current_price']),
                            stock_data['current_price']
                        ),
                        'expected_return': self._validate_expected_return(parsed_data.get('expected_return', 0)),
                        'time_horizon': self._validate_time_horizon(parsed_data.get('time_horizon', 30))
                    }

                    logger.info("DeepSeek响应解析成功")
                    return result

                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")

            # JSON解析失败，尝试文本解析
            return self._parse_text_response(llm_response, stock_data)

        except Exception as e:
            logger.error(f"解析DeepSeek响应失败: {str(e)}")
            # 返回基于文本的简单解析
            return self._parse_text_response(llm_response, stock_data)

    def _parse_text_response(self, text_response: str, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析文本格式的响应（备用方案）"""
        try:
            # 简单的文本解析逻辑
            text_lower = text_response.lower()

            # 推荐操作判断
            if '强烈建议买入' in text_response or 'strong_buy' in text_lower:
                recommendation = 'STRONG_BUY'
            elif '建议买入' in text_response or 'buy' in text_lower:
                recommendation = 'BUY'
            elif '建议卖出' in text_response or 'sell' in text_lower:
                recommendation = 'SELL'
            elif '强烈建议卖出' in text_response or 'strong_sell' in text_lower:
                recommendation = 'STRONG_SELL'
            else:
                recommendation = 'HOLD'

            # 提取置信度
            confidence = 0.7  # 默认置信度
            import re
            confidence_match = re.search(r'(\d+)%', text_response)
            if confidence_match:
                confidence = min(int(confidence_match.group(1)) / 100, 0.95)

            return {
                'recommendation': recommendation,
                'confidence': confidence,
                'summary': text_response[:300] + '...' if len(text_response) > 300 else text_response,
                'key_signals': ['DeepSeek AI分析完成'],
                'risk_warnings': ['请注意市场风险', '建议分散投资'],
                'target_price': stock_data['current_price'] * (1.05 if recommendation == 'BUY' else 0.95),
                'expected_return': 5 if recommendation == 'BUY' else -5,
                'time_horizon': 30
            }

        except Exception as e:
            logger.error(f"文本解析失败: {str(e)}")
            return self._get_default_analysis()

    def _validate_recommendation(self, recommendation: str) -> str:
        """验证推荐操作"""
        valid_recommendations = ['BUY', 'STRONG_BUY', 'HOLD', 'SELL', 'STRONG_SELL']
        return recommendation if recommendation in valid_recommendations else 'HOLD'

    def _validate_confidence(self, confidence: float) -> float:
        """验证置信度"""
        return max(30, min(95, float(confidence)))

    def _validate_target_price(self, target_price: float, current_price: float) -> float:
        """验证目标价格"""
        try:
            target = float(target_price)
            # 目标价格不应偏离当前价格太远（±50%）
            if target < current_price * 0.5 or target > current_price * 1.5:
                return current_price * 1.05  # 默认5%上涨
            return target
        except:
            return current_price * 1.05

    def _validate_expected_return(self, expected_return: float) -> float:
        """验证预期收益"""
        try:
            return max(-50, min(100, float(expected_return)))
        except:
            return 0

    def _validate_time_horizon(self, time_horizon: int) -> int:
        """验证时间周期"""
        try:
            return max(1, min(365, int(time_horizon)))
        except:
            return 30

    def _analyze_signals(self, indicators: Dict[str, Any]) -> Dict[str, int]:
        """分析技术指标信号"""
        signals = {'buy': 0, 'sell': 0, 'neutral': 0}
        signal_weights = {
            'rsi': 1.2,
            'macd': 1.5,
            'ma': 1.3,
            'bollinger': 1.0,
            'volume': 0.8,
            'momentum': 1.1
        }

        for indicator_name, indicator_data in indicators.items():
            if indicator_name in signal_weights:
                weight = signal_weights[indicator_name]
                signal = indicator_data.get('signal', 'NEUTRAL')
                strength = indicator_data.get('strength', 'WEAK')

                # 根据强度调整权重
                strength_multiplier = {
                    'STRONG': 1.5,
                    'MODERATE': 1.0,
                    'WEAK': 0.5
                }.get(strength, 1.0)

                adjusted_weight = weight * strength_multiplier

                if signal == 'BUY':
                    signals['buy'] += adjusted_weight
                elif signal == 'SELL':
                    signals['sell'] += adjusted_weight
                else:
                    signals['neutral'] += adjusted_weight

        return signals

    def _generate_recommendation(self, signals: Dict[str, int]) -> str:
        """生成投资建议"""
        buy_score = signals['buy']
        sell_score = signals['sell']
        neutral_score = signals['neutral']

        total_score = buy_score + sell_score + neutral_score
        if total_score == 0:
            return 'HOLD'

        buy_ratio = buy_score / total_score
        sell_ratio = sell_score / total_score

        if buy_ratio > 0.6:
            return 'BUY'
        elif sell_ratio > 0.6:
            return 'SELL'
        else:
            return 'HOLD'

    def _calculate_confidence(self, signals: Dict[str, int]) -> float:
        """计算置信度"""
        total_score = sum(signals.values())
        if total_score == 0:
            return 0.5

        max_score = max(signals.values())
        confidence = max_score / total_score

        # 调整到合理范围
        return min(max(confidence, 0.3), 0.95)

    def _generate_summary(self, symbol: str, stock_data: Dict[str, Any], recommendation: str, confidence: float) -> str:
        """生成分析摘要"""
        price_change = stock_data['price_change_percent']
        company_name = stock_data.get('company_name', symbol)

        templates = {
            'BUY': [
                f"{company_name}({symbol})技术指标显示强劲上涨潜力，建议买入。多项指标支持看涨观点，预期短期内有良好表现。",
                f"基于技术分析，{company_name}处于上升趋势中，多项指标发出买入信号，建议积极配置。",
                f"{company_name}技术面表现良好，突破关键阻力位，建议买入并持有。"
            ],
            'SELL': [
                f"{company_name}({symbol})技术指标显示下跌风险增加，建议减仓或卖出以规避风险。",
                f"多项技术指标转为看跌，{company_name}可能面临调整压力，建议谨慎操作。",
                f"{company_name}跌破关键支撑位，技术面走弱，建议及时止损。"
            ],
            'HOLD': [
                f"{company_name}({symbol})目前处于震荡整理阶段，建议持有观望，等待明确方向。",
                f"技术指标信号混合，{company_name}短期方向不明确，建议保持现有仓位。",
                f"{company_name}在关键位置附近震荡，建议持有并密切关注突破方向。"
            ]
        }

        summary_list = templates.get(recommendation, templates['HOLD'])
        base_summary = random.choice(summary_list)

        # 添加置信度信息
        confidence_text = f"分析置信度为{confidence*100:.0f}%"

        return f"{base_summary} {confidence_text}。"

    def _extract_key_signals(self, indicators: Dict[str, Any], signals: Dict[str, int]) -> List[str]:
        """提取关键信号"""
        key_signals = []

        for indicator_name, indicator_data in indicators.items():
            signal = indicator_data.get('signal', 'NEUTRAL')
            strength = indicator_data.get('strength', 'WEAK')

            if strength in ['STRONG', 'MODERATE'] and signal != 'NEUTRAL':
                description = indicator_data.get('description', '')
                if description:
                    key_signals.append(description)

        # 如果没有强信号，添加一些通用信号
        if not key_signals:
            key_signals = [
                "技术指标处于正常波动范围",
                "市场情绪相对稳定",
                "成交量维持在合理水平"
            ]

        return key_signals[:5]  # 最多返回5个关键信号

    def _identify_risks(self, stock_data: Dict[str, Any], indicators: Dict[str, Any]) -> List[str]:
        """识别风险警告"""
        risks = []

        # 价格波动风险
        price_change = abs(stock_data['price_change_percent'])
        if price_change > 5:
            risks.append(f"价格波动较大({price_change:.1f}%)，注意风险控制")

        # RSI风险
        rsi_data = indicators.get('rsi', {})
        rsi_value = rsi_data.get('value', 50)
        if rsi_value > 80:
            risks.append("RSI显示严重超买，注意回调风险")
        elif rsi_value < 20:
            risks.append("RSI显示严重超卖，可能存在反弹机会")

        # 成交量风险
        volume_data = indicators.get('volume', {})
        if volume_data.get('signal') == 'NEUTRAL' and '萎缩' in volume_data.get('description', ''):
            risks.append("成交量萎缩，市场参与度不高")

        # 通用风险提示
        general_risks = [
            "市场存在不确定性，建议分散投资",
            "注意宏观经济环境变化的影响",
            "建议设置止损位控制风险"
        ]

        if not risks:
            risks.extend(general_risks[:2])
        else:
            risks.append(random.choice(general_risks))

        return risks[:4]  # 最多返回4个风险警告

    def _calculate_target_price(self, stock_data: Dict[str, Any], recommendation: str) -> float:
        """计算目标价格"""
        current_price = stock_data['current_price']

        # 基于推荐类型设置目标价格
        if recommendation == 'BUY':
            # 买入建议：目标价格上涨5-15%
            return current_price * (1 + random.uniform(0.05, 0.15))
        elif recommendation == 'SELL':
            # 卖出建议：目标价格下跌5-10%
            return current_price * (1 - random.uniform(0.05, 0.10))
        else:
            # 持有建议：目标价格小幅波动
            return current_price * (1 + random.uniform(-0.03, 0.05))

    def _determine_time_horizon(self, recommendation: str, confidence: float) -> int:
        """确定投资时间周期"""
        base_days = {
            'BUY': 30,
            'SELL': 14,
            'HOLD': 21
        }

        days = base_days.get(recommendation, 21)

        # 根据置信度调整时间周期
        if confidence > 0.8:
            days = int(days * 1.2)
        elif confidence < 0.5:
            days = int(days * 0.8)

        return days

    def _get_default_analysis(self) -> Dict[str, Any]:
        """获取默认分析结果"""
        return {
            'recommendation': 'HOLD',
            'confidence': 0.5,
            'summary': '暂时无法完成详细分析，建议谨慎操作。',
            'key_signals': ['技术分析暂时不可用'],
            'risk_warnings': ['建议等待更多数据后再做决策'],
            'target_price': 0,
            'expected_return': 0,
            'time_horizon': 30
        }

    async def generate_summary(self, symbol: str, stock_data: Dict[str, Any]) -> str:
        """生成简要摘要"""
        try:
            price_change = stock_data['price_change_percent']
            current_price = stock_data['current_price']

            if price_change > 2:
                trend = "强势上涨"
            elif price_change > 0:
                trend = "小幅上涨"
            elif price_change > -2:
                trend = "小幅下跌"
            else:
                trend = "明显下跌"

            return f"{symbol}当前价格${current_price:.2f}，今日{trend}{abs(price_change):.1f}%。技术面需要进一步观察。"

        except Exception as e:
            logger.error(f"生成摘要失败: {str(e)}")
            return f"{symbol}数据获取中，请稍后查看详细分析。"
