{"name": "quant-analysis-system", "version": "1.0.0", "description": "智能量化分析系统 - 专为小白用户设计", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "build": "cd frontend && npm run build", "install:all": "npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && pip install -r requirements.txt", "test": "cd frontend && npm test", "lint": "cd frontend && npm run lint"}, "keywords": ["quantitative-analysis", "stock-analysis", "ai-prediction", "backtesting", "technical-indicators"], "author": "Quant Analysis Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}