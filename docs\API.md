# API 文档

## 概述

智能量化分析系统提供RESTful API，支持股票分析、价格预测和策略回测功能。

## 基础信息

- **基础URL**: `http://localhost:8000/api`
- **认证**: 暂无需认证
- **数据格式**: JSON
- **字符编码**: UTF-8

## 分析接口

### 1. 股票分析

**接口**: `GET /analyze/{symbol}`

**描述**: 对指定股票进行AI智能分析

**参数**:
- `symbol` (路径参数): 股票代码，如 AAPL, TSLA

**响应示例**:
```json
{
  "symbol": "AAPL",
  "company_name": "Apple Inc.",
  "current_price": 150.25,
  "price_change": 2.15,
  "price_change_percent": 1.45,
  "recommendation": "BUY",
  "confidence": 0.85,
  "summary": "苹果公司技术指标显示强劲上涨潜力，建议买入。",
  "key_signals": [
    "RSI突破50，显示买入信号",
    "MACD金叉，上涨趋势确立"
  ],
  "risk_warnings": [
    "注意大盘走势影响",
    "建议设置止损位"
  ],
  "target_price": 165.50,
  "expected_return": 10.15,
  "time_horizon": 30,
  "technical_indicators": {},
  "price_data": []
}
```

### 2. 技术指标

**接口**: `GET /indicators/{symbol}`

**描述**: 获取股票的技术指标数据

**参数**:
- `symbol` (路径参数): 股票代码

**响应示例**:
```json
{
  "symbol": "AAPL",
  "indicators": {
    "rsi": {
      "name": "RSI",
      "value": 65.5,
      "signal": "BUY",
      "strength": "MODERATE",
      "description": "RSI=65.5，处于正常区间"
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 预测接口

### 1. 价格预测

**接口**: `GET /predict/{symbol}`

**描述**: 预测股票未来价格走势

**参数**:
- `symbol` (路径参数): 股票代码
- `days` (查询参数): 预测天数，默认30天

**响应示例**:
```json
{
  "symbol": "AAPL",
  "current_price": 150.25,
  "prediction_horizon": 30,
  "predictions": [
    {
      "date": "2024-01-02",
      "predicted_price": 152.30,
      "confidence": 0.85,
      "lower_bound": 148.50,
      "upper_bound": 156.10
    }
  ],
  "trend_direction": "BULLISH",
  "trend_strength": "MODERATE",
  "overall_confidence": 0.78,
  "risk_level": "MODERATE",
  "model_used": "ensemble",
  "generated_at": "2024-01-01T12:00:00Z"
}
```

### 2. 自定义预测

**接口**: `POST /predict`

**描述**: 使用自定义参数进行预测

**请求体**:
```json
{
  "symbol": "AAPL",
  "prediction_days": 30,
  "model_type": "ensemble",
  "confidence_threshold": 0.7
}
```

## 回测接口

### 1. 策略回测

**接口**: `POST /backtest`

**描述**: 对交易策略进行历史回测

**请求体**:
```json
{
  "symbol": "AAPL",
  "strategy": "ma_crossover",
  "start_date": "2023-01-01",
  "end_date": "2024-01-01",
  "initial_capital": 10000,
  "commission": 0.001
}
```

**响应示例**:
```json
{
  "symbol": "AAPL",
  "strategy": "ma_crossover",
  "period": "2023-01-01 to 2024-01-01",
  "performance": {
    "total_return": 15.6,
    "annual_return": 15.6,
    "sharpe_ratio": 1.23,
    "max_drawdown": -8.5,
    "win_rate": 62.5,
    "profit_factor": 2.25,
    "total_trades": 24,
    "winning_trades": 15,
    "losing_trades": 9,
    "avg_win": 4.2,
    "avg_loss": -2.8
  },
  "equity_curve": [],
  "trades": [],
  "strategy_description": "移动平均交叉策略",
  "risk_analysis": {},
  "generated_at": "2024-01-01T12:00:00Z"
}
```

### 2. 快速回测

**接口**: `GET /backtest/{symbol}/{strategy}`

**描述**: 使用默认参数进行快速回测

**参数**:
- `symbol` (路径参数): 股票代码
- `strategy` (路径参数): 策略名称

## 工具接口

### 1. 可用策略

**接口**: `GET /strategies`

**描述**: 获取所有可用的交易策略

**响应示例**:
```json
{
  "strategies": [
    {
      "name": "ma_crossover",
      "display_name": "移动平均交叉",
      "description": "基于短期和长期移动平均线的交叉信号",
      "risk_level": "MODERATE",
      "best_for": "趋势市场"
    }
  ]
}
```

### 2. 可用模型

**接口**: `GET /models`

**描述**: 获取所有可用的预测模型

**响应示例**:
```json
{
  "models": [
    {
      "name": "ensemble",
      "description": "集成学习模型",
      "accuracy": 0.75,
      "best_for": "中长期预测"
    }
  ],
  "default_model": "ensemble"
}
```

## 错误处理

所有API在出错时返回标准错误格式：

```json
{
  "detail": "错误描述信息"
}
```

常见HTTP状态码：
- `200`: 成功
- `404`: 资源未找到
- `422`: 请求参数错误
- `500`: 服务器内部错误

## 限制说明

- 请求频率限制: 暂无限制
- 数据更新频率: 实时获取
- 支持的股票市场: 主要支持美股
- 历史数据范围: 最多1年

## 联系方式

如有问题，请查看项目文档或提交Issue。
