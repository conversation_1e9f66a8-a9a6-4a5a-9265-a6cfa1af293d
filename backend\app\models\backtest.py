from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

class BacktestRequest(BaseModel):
    """回测请求模型"""
    symbol: str = Field(..., description="股票代码")
    strategy: str = Field(..., description="策略名称")
    start_date: str = Field(..., description="开始日期 YYYY-MM-DD")
    end_date: str = Field(..., description="结束日期 YYYY-MM-DD")
    initial_capital: float = Field(10000, gt=0, description="初始资金")
    commission: float = Field(0.001, ge=0, le=0.1, description="手续费率")
    
    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "strategy": "ma_crossover",
                "start_date": "2023-01-01",
                "end_date": "2024-01-01",
                "initial_capital": 10000,
                "commission": 0.001
            }
        }

class TradeRecord(BaseModel):
    """交易记录模型"""
    date: str = Field(..., description="交易日期")
    action: str = Field(..., description="交易动作: BUY, SELL")
    price: float = Field(..., description="交易价格")
    quantity: int = Field(..., description="交易数量")
    commission: float = Field(..., description="手续费")
    portfolio_value: float = Field(..., description="组合价值")

class PerformanceMetrics(BaseModel):
    """绩效指标模型"""
    total_return: float = Field(..., description="总收益率 (%)")
    annual_return: float = Field(..., description="年化收益率 (%)")
    sharpe_ratio: float = Field(..., description="夏普比率")
    max_drawdown: float = Field(..., description="最大回撤 (%)")
    win_rate: float = Field(..., description="胜率 (%)")
    profit_factor: float = Field(..., description="盈亏比")
    total_trades: int = Field(..., description="总交易次数")
    winning_trades: int = Field(..., description="盈利交易次数")
    losing_trades: int = Field(..., description="亏损交易次数")
    avg_win: float = Field(..., description="平均盈利 (%)")
    avg_loss: float = Field(..., description="平均亏损 (%)")

class EquityCurvePoint(BaseModel):
    """权益曲线点"""
    date: str = Field(..., description="日期")
    portfolio_value: float = Field(..., description="组合价值")
    benchmark_value: float = Field(..., description="基准价值")
    drawdown: float = Field(..., description="回撤")

class BacktestResponse(BaseModel):
    """回测响应模型"""
    symbol: str = Field(..., description="股票代码")
    strategy: str = Field(..., description="策略名称")
    period: str = Field(..., description="回测周期")
    performance: PerformanceMetrics = Field(..., description="绩效指标")
    equity_curve: List[EquityCurvePoint] = Field(..., description="权益曲线")
    trades: List[TradeRecord] = Field(..., description="交易记录")
    strategy_description: str = Field(..., description="策略描述")
    risk_analysis: Dict[str, Any] = Field(..., description="风险分析")
    generated_at: str = Field(..., description="生成时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "strategy": "ma_crossover",
                "period": "2023-01-01 to 2024-01-01",
                "performance": {
                    "total_return": 15.6,
                    "annual_return": 15.6,
                    "sharpe_ratio": 1.23,
                    "max_drawdown": -8.5,
                    "win_rate": 62.5,
                    "profit_factor": 2.25,
                    "total_trades": 24,
                    "winning_trades": 15,
                    "losing_trades": 9,
                    "avg_win": 4.2,
                    "avg_loss": -2.8
                },
                "equity_curve": [],
                "trades": [],
                "strategy_description": "移动平均交叉策略",
                "risk_analysis": {},
                "generated_at": "2024-01-01T12:00:00Z"
            }
        }
