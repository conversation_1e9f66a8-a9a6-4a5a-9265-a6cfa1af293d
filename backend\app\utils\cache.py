import json
import asyncio
from typing import Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class SimpleCache:
    """简单内存缓存实现"""

    def __init__(self):
        self._cache = {}
        self._expiry = {}

    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            if key in self._cache:
                # 检查是否过期
                if key in self._expiry and datetime.now() > self._expiry[key]:
                    await self.delete(key)
                    return None
                return self._cache[key]
            return None
        except Exception as e:
            logger.error(f"缓存获取失败 {key}: {str(e)}")
            return None

    async def set(self, key: str, value: Any, expire_seconds: int = 300) -> bool:
        """设置缓存值"""
        try:
            self._cache[key] = value
            if expire_seconds > 0:
                self._expiry[key] = datetime.now() + timedelta(seconds=expire_seconds)
            return True
        except Exception as e:
            logger.error(f"缓存设置失败 {key}: {str(e)}")
            return False

    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            if key in self._cache:
                del self._cache[key]
            if key in self._expiry:
                del self._expiry[key]
            return True
        except Exception as e:
            logger.error(f"缓存删除失败 {key}: {str(e)}")
            return False

    async def clear(self) -> bool:
        """清空所有缓存"""
        try:
            self._cache.clear()
            self._expiry.clear()
            return True
        except Exception as e:
            logger.error(f"缓存清空失败: {str(e)}")
            return False

    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)

    async def cleanup_expired(self):
        """清理过期缓存"""
        try:
            now = datetime.now()
            expired_keys = [
                key for key, expiry in self._expiry.items()
                if now > expiry
            ]

            for key in expired_keys:
                await self.delete(key)

            logger.info(f"清理过期缓存: {len(expired_keys)} 个")
        except Exception as e:
            logger.error(f"清理过期缓存失败: {str(e)}")

# 创建全局缓存实例
cache_result = SimpleCache()

# 定期清理过期缓存
async def periodic_cleanup():
    """定期清理任务"""
    while True:
        await asyncio.sleep(300)  # 每5分钟清理一次
        await cache_result.cleanup_expired()

# 清理任务将在应用启动时启动
# asyncio.create_task(periodic_cleanup())
