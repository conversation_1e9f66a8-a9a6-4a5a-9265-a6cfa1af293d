from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, List
import logging

from app.services.backtest_engine import BacktestEngine
from app.models.backtest import BacktestResponse, BacktestRequest
from app.utils.cache import cache_result

router = APIRouter()
logger = logging.getLogger(__name__)

# 初始化回测引擎
backtest_engine = BacktestEngine()

@router.post("/backtest", response_model=BacktestResponse)
async def run_backtest(request: BacktestRequest, background_tasks: BackgroundTasks):
    """
    📊 策略回测
    
    对交易策略进行历史回测
    """
    try:
        symbol = request.symbol.upper()
        strategy = request.strategy
        logger.info(f"开始回测: {symbol}, 策略: {strategy}")
        
        # 检查缓存
        cache_key = f"backtest:{symbol}:{strategy}:{request.start_date}:{request.end_date}"
        cached_result = await cache_result.get(cache_key)
        if cached_result:
            logger.info(f"返回缓存回测结果: {symbol}")
            return cached_result
        
        # 执行回测
        backtest_result = await backtest_engine.run_backtest(request)
        
        if not backtest_result:
            raise HTTPException(
                status_code=404,
                detail=f"无法完成股票 {symbol} 的回测"
            )
        
        # 缓存结果 (30分钟)
        background_tasks.add_task(cache_result.set, cache_key, backtest_result, 1800)
        
        logger.info(f"回测完成: {symbol}")
        return backtest_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"回测失败 {request.symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="回测服务暂时不可用，请稍后重试"
        )

@router.get("/backtest/{symbol}/{strategy}")
async def quick_backtest(symbol: str, strategy: str, background_tasks: BackgroundTasks):
    """
    🚀 快速回测
    
    使用默认参数进行快速回测
    """
    try:
        symbol = symbol.upper()
        logger.info(f"快速回测: {symbol}, 策略: {strategy}")
        
        # 创建默认回测请求
        request = BacktestRequest(
            symbol=symbol,
            strategy=strategy,
            start_date="2023-01-01",
            end_date="2024-01-01",
            initial_capital=10000,
            commission=0.001
        )
        
        # 执行回测
        backtest_result = await backtest_engine.run_backtest(request)
        
        if not backtest_result:
            raise HTTPException(
                status_code=404,
                detail=f"无法完成股票 {symbol} 的快速回测"
            )
        
        logger.info(f"快速回测完成: {symbol}")
        return backtest_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"快速回测失败 {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="快速回测服务暂时不可用"
        )

@router.get("/strategies")
async def get_available_strategies():
    """
    📚 获取可用策略
    
    返回所有可用的交易策略
    """
    try:
        strategies = await backtest_engine.get_available_strategies()
        return {
            "strategies": strategies,
            "description": "支持多种经典交易策略"
        }
    except Exception as e:
        logger.error(f"获取策略列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="策略服务暂时不可用"
        )

@router.get("/performance/{symbol}")
async def get_strategy_performance(symbol: str):
    """
    📈 策略表现对比
    
    对比不同策略在特定股票上的表现
    """
    try:
        symbol = symbol.upper()
        performance_data = await backtest_engine.compare_strategies(symbol)
        
        return {
            "symbol": symbol,
            "strategy_comparison": performance_data,
            "recommendation": "基于历史表现选择最适合的策略"
        }
        
    except Exception as e:
        logger.error(f"获取策略表现失败 {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="策略表现服务暂时不可用"
        )
