# 🔗 Tushare真实数据配置指南

## 📋 当前状态

✅ **Tushare已安装**: 版本 1.2.89  
✅ **免费API可用**: 可获取实时股价数据  
✅ **系统集成**: A股分析器已支持tushare数据  
⚠️ **Pro API**: 需要token才能使用高级功能  

## 🎯 数据源配置

### 当前数据流程
1. **优先级1**: Tushare Pro API (需要token)
2. **优先级2**: Tushare免费API (已可用)
3. **优先级3**: 模拟数据 (备用方案)

### 免费API功能
- ✅ 实时股价获取
- ✅ 基本股票信息
- ✅ 简单历史数据
- ⚠️ 数据频率限制
- ⚠️ 功能相对有限

## 🔑 获取Tushare Pro Token

### 步骤1: 注册账号
1. 访问 [https://tushare.pro/](https://tushare.pro/)
2. 注册新账号
3. 完成邮箱验证

### 步骤2: 获取Token
1. 登录tushare.pro
2. 进入"个人中心"
3. 查看"接口TOKEN"
4. 复制你的专属token

### 步骤3: 配置Token
```bash
# Windows PowerShell
$env:TUSHARE_TOKEN = "your_token_here"

# Windows CMD
set TUSHARE_TOKEN=your_token_here

# Linux/Mac
export TUSHARE_TOKEN=your_token_here
```

### 步骤4: 重启系统
```bash
python backend/start_server.py
```

## 📊 数据对比

### 免费API vs Pro API

| 功能 | 免费API | Pro API |
|------|---------|---------|
| 实时价格 | ✅ | ✅ |
| 历史数据 | 有限 | 完整 |
| 基本信息 | ✅ | ✅ |
| 财务数据 | ❌ | ✅ |
| 技术指标 | 基础 | 高级 |
| 请求频率 | 限制 | 更高 |
| 数据质量 | 良好 | 优秀 |

## 🧪 测试真实数据

### 验证数据源
```python
# 运行验证脚本
python verify_real_data.py

# 或者直接访问API
curl http://localhost:8000/api/analyze/000001
```

### 检查数据字段
```json
{
  "symbol": "000001",
  "company_name": "平安银行",
  "current_price": 12.61,
  "data_source": "tushare_free_api",  // 关键字段
  "timestamp": "2025-07-21T20:30:00"
}
```

## 🔧 系统配置

### A股分析器配置
```python
class AStockAnalyzer:
    def __init__(self):
        # 自动检测tushare可用性
        if TUSHARE_AVAILABLE:
            # 优先使用Pro API
            if token:
                self.pro = ts.pro_api()
            else:
                # 降级到免费API
                self.pro = None
```

### 数据获取流程
```python
async def get_stock_data(self, symbol: str):
    if self.pro:
        # 使用Pro API
        return await self._get_pro_api_data(symbol)
    else:
        # 使用免费API
        return await self._get_free_api_data(symbol)
```

## 📈 真实数据示例

### 平安银行 (000001)
```json
{
  "symbol": "000001",
  "ts_code": "000001.SZ",
  "company_name": "平安银行",
  "current_price": 12.61,
  "price_change": -0.09,
  "price_change_percent": -0.71,
  "volume": 45678900,
  "market": "主板",
  "industry": "银行",
  "area": "深圳",
  "data_source": "tushare_free_api",
  "timestamp": "2025-07-21T20:30:00"
}
```

## 🎯 优化建议

### 短期优化
1. **申请Pro Token**: 获得更好的数据质量
2. **缓存策略**: 减少API调用频率
3. **错误处理**: 完善数据获取异常处理

### 长期规划
1. **多数据源**: 集成更多数据提供商
2. **实时推送**: WebSocket实时数据流
3. **数据质量**: 数据清洗和验证机制

## 🚨 注意事项

### 使用限制
- 免费API有调用频率限制
- 部分高级功能需要Pro账号
- 数据延迟可能存在

### 最佳实践
- 合理设置缓存时间
- 避免频繁调用API
- 监控API调用状态
- 准备备用数据源

## 🔗 相关链接

- [Tushare官网](https://tushare.pro/)
- [Tushare文档](https://tushare.pro/document/2)
- [API接口说明](https://tushare.pro/document/2?doc_id=25)
- [积分获取规则](https://tushare.pro/document/1?doc_id=13)

## 📞 技术支持

### 问题排查
1. 检查tushare安装: `pip list | grep tushare`
2. 验证网络连接: `ping tushare.pro`
3. 测试API调用: `python test_tushare_connection.py`
4. 查看系统日志: 检查后端服务日志

### 常见问题
- **Q**: 为什么显示模拟数据？
- **A**: 可能是网络问题或API限制，检查日志

- **Q**: 如何提高数据质量？
- **A**: 申请Pro token并配置环境变量

- **Q**: 数据更新频率如何？
- **A**: 免费API有限制，Pro API更频繁

---

**🎉 Tushare真实数据已成功集成到A股分析系统！**

*现在可以获取真实的A股市场数据进行分析* 📈🇨🇳
