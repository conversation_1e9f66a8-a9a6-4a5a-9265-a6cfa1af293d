import { useState } from 'react'
import axios from 'axios'

interface AnalysisData {
  symbol: string
  company_name: string
  current_price: number
  price_change: number
  price_change_percent: number
  recommendation: 'BUY' | 'SELL' | 'HOLD'
  confidence: number
  summary: string
  key_signals: string[]
  risk_warnings: string[]
  target_price: number
  expected_return: number
  time_horizon: number
  technical_indicators: any
  price_data: any[]
}

export const useAnalysis = () => {
  const [analysis, setAnalysis] = useState<AnalysisData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const analyzeStock = async (symbol: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await axios.get(`/api/analyze/${symbol}`)
      setAnalysis(response.data)
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || '分析失败，请检查股票代码是否正确'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const clearAnalysis = () => {
    setAnalysis(null)
    setError(null)
  }

  return {
    analysis,
    loading,
    error,
    analyzeStock,
    clearAnalysis
  }
}
