#!/usr/bin/env python3
"""
测试DeepSeek AI分析在原前端页面的集成
"""

import requests
import json
import time

def test_deepseek_ai_analysis():
    """测试DeepSeek AI分析功能"""
    print("🤖 测试原前端页面DeepSeek AI分析集成")
    print("="*60)
    
    test_symbols = ["000001", "600519", "000858"]
    
    for symbol in test_symbols:
        print(f"\n📊 测试股票: {symbol}")
        print("-" * 30)
        
        try:
            start_time = time.time()
            response = requests.get(f"http://localhost:8000/api/analyze/{symbol}", timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ 分析成功 (耗时: {end_time-start_time:.2f}秒)")
                
                # 检查分析类型
                analysis_type = data.get('analysis_type', 'unknown')
                print(f"🔍 分析引擎: {get_analysis_engine_name(analysis_type)}")
                
                # 检查专业级功能
                if data.get('overall_score') is not None:
                    print(f"🏛️ 专业级评分: {data['overall_score']:.1f}/100")
                    print(f"   技术面: {data.get('technical_score', 'N/A')}")
                    print(f"   基本面: {data.get('fundamental_score', 'N/A')}")
                    print(f"   市场环境: {data.get('market_score', 'N/A')}")
                    print(f"   风险控制: {data.get('risk_score', 'N/A')}")
                
                # 检查AI摘要
                summary = data.get('summary', '')
                if summary:
                    print(f"📝 AI摘要长度: {len(summary)}字符")
                    if len(summary) > 200:
                        print("   ✅ 检测到详细AI分析")
                    else:
                        print("   ⚠️ 摘要较短，可能使用规则引擎")
                
                # 检查关键信号
                key_signals = data.get('key_signals', [])
                print(f"🔍 关键信号: {len(key_signals)}个")
                
                # 检查风险警示
                risk_warnings = data.get('risk_warnings', [])
                print(f"⚠️ 风险警示: {len(risk_warnings)}个")
                
                # 检查投资建议
                recommendation = data.get('recommendation', 'HOLD')
                confidence = data.get('confidence', 0)
                print(f"💡 投资建议: {get_recommendation_text(recommendation)}")
                print(f"🎯 AI信心指数: {confidence*100:.1f}%")
                
                # 检查前端显示特征
                frontend_features = check_frontend_features(data)
                print(f"🎨 前端显示特征:")
                for feature, status in frontend_features.items():
                    icon = "✅" if status else "❌"
                    print(f"   {icon} {feature}")
                
            else:
                print(f"❌ API错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def get_analysis_engine_name(analysis_type):
    """获取分析引擎名称"""
    if 'deepseek' in analysis_type.lower():
        return "🤖 DeepSeek AI大模型"
    elif 'ai' in analysis_type.lower():
        return "🧠 AI增强分析"
    elif 'enhanced' in analysis_type.lower():
        return "🔧 增强规则引擎"
    else:
        return f"❓ {analysis_type}"

def check_frontend_features(data):
    """检查前端显示特征"""
    features = {}
    
    # AI状态指示器支持
    features['AI状态指示器'] = 'analysis_type' in data
    
    # 专业级评分圆环
    features['综合评分圆环'] = data.get('overall_score') is not None
    
    # 分维度进度条
    features['分维度进度条'] = all(
        data.get(field) is not None 
        for field in ['technical_score', 'fundamental_score', 'market_score', 'risk_score']
    )
    
    # AI摘要格式化
    summary = data.get('summary', '')
    features['AI摘要格式化'] = len(summary) > 100
    
    # 关键信号列表
    features['关键信号列表'] = len(data.get('key_signals', [])) > 0
    
    # 风险警示显示
    features['风险警示显示'] = len(data.get('risk_warnings', [])) > 0
    
    # AI信心指数条
    features['AI信心指数条'] = data.get('confidence') is not None
    
    return features

def get_recommendation_text(recommendation):
    """获取推荐操作中文"""
    texts = {
        'STRONG_BUY': '🚀 强烈建议买入',
        'BUY': '📈 建议买入',
        'HOLD': '🤝 建议持有',
        'SELL': '📉 建议卖出',
        'STRONG_SELL': '⚠️ 强烈建议卖出'
    }
    return texts.get(recommendation, '🤝 建议持有')

def test_frontend_enhancements():
    """测试前端增强功能"""
    print("\n🎨 前端DeepSeek AI增强功能")
    print("="*40)
    
    print("✅ 已实现的增强功能:")
    print("   🤖 AI状态指示器:")
    print("      • 实时显示分析引擎类型")
    print("      • DeepSeek AI / AI增强 / 规则引擎")
    print("      • 彩色状态点和文字")
    
    print("\n   📊 分析计数器:")
    print("      • 统计已分析次数")
    print("      • 动画效果更新")
    
    print("\n   🎨 AI摘要增强:")
    print("      • 自动检测DeepSeek内容")
    print("      • 长文本自动分段")
    print("      • 分析类型标识")
    
    print("\n   🔍 关键信号优化:")
    print("      • 编号列表显示")
    print("      • 信号数量徽章")
    print("      • 紫色主题样式")
    
    print("\n   ⚠️ 风险警示美化:")
    print("      • 警告图标和边框")
    print("      • 黄色警示主题")
    print("      • 数量统计显示")
    
    print("\n   📈 专业级评分:")
    print("      • 综合评分圆环")
    print("      • 分维度进度条")
    print("      • 动态颜色变化")
    
    print("\n   🎯 AI信心指数:")
    print("      • 渐变进度条")
    print("      • 百分比显示")
    print("      • 动画效果")

def main():
    """主函数"""
    print("🤖 原前端页面DeepSeek AI集成测试")
    print("="*60)
    
    # 测试后端AI分析
    test_deepseek_ai_analysis()
    
    # 测试前端增强功能
    test_frontend_enhancements()
    
    print("\n" + "="*60)
    print("🎉 原前端页面DeepSeek AI集成测试完成")
    print("="*60)
    
    print("\n📋 集成总结:")
    print("✅ 在原dark.html页面成功集成DeepSeek AI分析")
    print("✅ AI状态指示器实时显示分析引擎类型")
    print("✅ 专业级评分可视化（圆环+进度条）")
    print("✅ AI摘要智能格式化和分段显示")
    print("✅ 关键信号和风险警示美化显示")
    print("✅ 分析计数器和AI信心指数可视化")
    
    print("\n🎯 核心特色:")
    print("🤖 DeepSeek AI大模型无缝集成")
    print("🏛️ 保持原页面风格的专业级升级")
    print("📊 四维度综合评分系统")
    print("🎨 增强的AI分析结果展示")
    print("⚡ 实时AI状态监控和反馈")
    
    print("\n🔗 使用方法:")
    print("   1. 打开 dark.html 页面")
    print("   2. 观察右上角AI状态指示器")
    print("   3. 输入股票代码（如：000001, 600519）")
    print("   4. 点击'🚀 AI分析'按钮")
    print("   5. 观察AI分析类型标识和结果展示")
    print("   6. 查看专业级评分和可视化效果")
    
    print("\n📱 访问地址:")
    print("   前端页面: file:///C:/Users/<USER>/Desktop/qwh/A-AI/frontend/dark.html")
    print("   后端API: http://localhost:8000")
    
    print("\n🎊 恭喜！DeepSeek AI分析功能已成功集成到原前端页面！")
    print("   现在您可以在熟悉的界面中享受专业级AI投资分析服务！")
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
