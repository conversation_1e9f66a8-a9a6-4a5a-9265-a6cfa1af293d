from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, List
import logging

from app.services.prediction_engine import PredictionEngine
from app.models.prediction import PredictionResponse, PredictionRequest
from app.utils.cache import cache_result

router = APIRouter()
logger = logging.getLogger(__name__)

# 初始化预测引擎
prediction_engine = PredictionEngine()

@router.get("/predict/{symbol}", response_model=PredictionResponse)
async def predict_stock_price(symbol: str, days: int = 30, background_tasks: BackgroundTasks):
    """
    🔮 股价预测
    
    基于机器学习模型预测股价走势
    """
    try:
        symbol = symbol.upper()
        logger.info(f"开始预测股票: {symbol}, 预测天数: {days}")
        
        # 检查缓存
        cache_key = f"prediction:{symbol}:{days}"
        cached_result = await cache_result.get(cache_key)
        if cached_result:
            logger.info(f"返回缓存预测结果: {symbol}")
            return cached_result
        
        # 执行预测
        prediction_result = await prediction_engine.predict_price(symbol, days)
        
        if not prediction_result:
            raise HTTPException(
                status_code=404,
                detail=f"无法获取股票 {symbol} 的预测数据"
            )
        
        # 缓存结果 (10分钟)
        background_tasks.add_task(cache_result.set, cache_key, prediction_result, 600)
        
        logger.info(f"预测完成: {symbol}")
        return prediction_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预测失败 {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="预测服务暂时不可用，请稍后重试"
        )

@router.post("/predict", response_model=PredictionResponse)
async def predict_with_config(request: PredictionRequest, background_tasks: BackgroundTasks):
    """
    🔮 自定义预测配置
    
    使用自定义参数进行股价预测
    """
    try:
        symbol = request.symbol.upper()
        logger.info(f"自定义预测: {symbol}, 配置: {request.dict()}")
        
        # 执行预测
        prediction_result = await prediction_engine.predict_with_config(request)
        
        if not prediction_result:
            raise HTTPException(
                status_code=404,
                detail=f"无法完成股票 {symbol} 的预测"
            )
        
        logger.info(f"自定义预测完成: {symbol}")
        return prediction_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"自定义预测失败 {request.symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="预测服务暂时不可用，请稍后重试"
        )

@router.get("/models")
async def get_available_models():
    """
    🧠 获取可用模型
    
    返回所有可用的预测模型信息
    """
    try:
        models = await prediction_engine.get_available_models()
        return {
            "models": models,
            "default_model": "ensemble",
            "description": "多模型融合预测系统"
        }
    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="模型服务暂时不可用"
        )

@router.get("/confidence/{symbol}")
async def get_prediction_confidence(symbol: str):
    """
    📊 获取预测置信度
    
    返回模型对特定股票的预测置信度
    """
    try:
        symbol = symbol.upper()
        confidence_data = await prediction_engine.get_confidence_metrics(symbol)
        
        return {
            "symbol": symbol,
            "confidence_metrics": confidence_data,
            "recommendation": "基于历史准确率评估模型可信度"
        }
        
    except Exception as e:
        logger.error(f"获取置信度失败 {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="置信度服务暂时不可用"
        )
