/**
 * 格式化工具函数
 */

// 格式化价格
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price)
}

// 格式化百分比
export const formatPercent = (value: number, decimals: number = 2): string => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`
}

// 格式化大数字
export const formatLargeNumber = (num: number): string => {
  if (num >= 1e9) {
    return `${(num / 1e9).toFixed(1)}B`
  } else if (num >= 1e6) {
    return `${(num / 1e6).toFixed(1)}M`
  } else if (num >= 1e3) {
    return `${(num / 1e3).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化日期
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

// 格式化相对时间
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes}分钟前`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}小时前`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}天前`
  }
}

// 获取推荐颜色
export const getRecommendationColor = (recommendation: string): string => {
  switch (recommendation) {
    case 'BUY':
      return 'text-success-600'
    case 'SELL':
      return 'text-danger-600'
    case 'HOLD':
      return 'text-warning-600'
    default:
      return 'text-gray-600'
  }
}

// 获取推荐背景色
export const getRecommendationBgColor = (recommendation: string): string => {
  switch (recommendation) {
    case 'BUY':
      return 'bg-success-50 border-success-200'
    case 'SELL':
      return 'bg-danger-50 border-danger-200'
    case 'HOLD':
      return 'bg-warning-50 border-warning-200'
    default:
      return 'bg-gray-50 border-gray-200'
  }
}

// 获取趋势颜色
export const getTrendColor = (value: number): string => {
  if (value > 0) {
    return 'text-success-600'
  } else if (value < 0) {
    return 'text-danger-600'
  }
  return 'text-gray-600'
}

// 格式化置信度
export const formatConfidence = (confidence: number): string => {
  return `${(confidence * 100).toFixed(0)}%`
}

// 获取信号强度文本
export const getSignalStrengthText = (strength: string): string => {
  switch (strength) {
    case 'STRONG':
      return '强'
    case 'MODERATE':
      return '中'
    case 'WEAK':
      return '弱'
    default:
      return '未知'
  }
}

// 获取信号文本
export const getSignalText = (signal: string): string => {
  switch (signal) {
    case 'BUY':
      return '买入'
    case 'SELL':
      return '卖出'
    case 'NEUTRAL':
      return '中性'
    default:
      return '未知'
  }
}

// 获取风险等级文本
export const getRiskLevelText = (level: string): string => {
  switch (level) {
    case 'LOW':
      return '低风险'
    case 'MODERATE':
      return '中等风险'
    case 'HIGH':
      return '高风险'
    default:
      return '未知风险'
  }
}

// 获取风险等级颜色
export const getRiskLevelColor = (level: string): string => {
  switch (level) {
    case 'LOW':
      return 'text-success-600'
    case 'MODERATE':
      return 'text-warning-600'
    case 'HIGH':
      return 'text-danger-600'
    default:
      return 'text-gray-600'
  }
}
