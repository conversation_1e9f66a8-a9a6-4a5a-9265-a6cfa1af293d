#!/usr/bin/env python3
"""
A股量化分析系统启动脚本
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(Path(__file__).parent.parent / '.env')

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置环境变量
os.environ['PYTHONPATH'] = str(current_dir)

if __name__ == "__main__":
    import uvicorn

    print("🚀 启动A股量化分析系统...")
    print(f"📁 工作目录: {current_dir}")
    print(f"🐍 Python路径: {sys.path[0]}")

    try:
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
