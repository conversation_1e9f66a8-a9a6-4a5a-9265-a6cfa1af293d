import React from 'react'
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  MinusIcon,
  FireIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

interface TechnicalIndicator {
  name: string
  value: number
  signal: 'BUY' | 'SELL' | 'NEUTRAL'
  strength: 'STRONG' | 'WEAK' | 'MODERATE'
  description: string
}

interface IndicatorDashboardProps {
  indicators: {
    rsi: TechnicalIndicator
    macd: TechnicalIndicator
    ma: TechnicalIndicator
    bollinger: TechnicalIndicator
    volume: TechnicalIndicator
    momentum: TechnicalIndicator
  }
}

export const IndicatorDashboard: React.FC<IndicatorDashboardProps> = ({ indicators }) => {
  const getSignalIcon = (signal: string) => {
    switch (signal) {
      case 'BUY':
        return <ArrowUpIcon className="w-4 h-4" />
      case 'SELL':
        return <ArrowDownIcon className="w-4 h-4" />
      case 'NEUTRAL':
        return <MinusIcon className="w-4 h-4" />
      default:
        return null
    }
  }

  const getSignalColor = (signal: string, strength: string) => {
    const baseColors = {
      BUY: 'success',
      SELL: 'danger',
      NEUTRAL: 'warning'
    }
    
    const color = baseColors[signal as keyof typeof baseColors] || 'gray'
    const opacity = strength === 'STRONG' ? '600' : strength === 'MODERATE' ? '500' : '400'
    
    return `text-${color}-${opacity} bg-${color}-50 border-${color}-200`
  }

  const getStrengthIcon = (strength: string) => {
    switch (strength) {
      case 'STRONG':
        return <FireIcon className="w-4 h-4" />
      case 'MODERATE':
        return <CheckCircleIcon className="w-4 h-4" />
      case 'WEAK':
        return <ExclamationTriangleIcon className="w-4 h-4" />
      default:
        return null
    }
  }

  const getSignalText = (signal: string) => {
    switch (signal) {
      case 'BUY':
        return '买入'
      case 'SELL':
        return '卖出'
      case 'NEUTRAL':
        return '中性'
      default:
        return '未知'
    }
  }

  const getStrengthText = (strength: string) => {
    switch (strength) {
      case 'STRONG':
        return '强'
      case 'MODERATE':
        return '中'
      case 'WEAK':
        return '弱'
      default:
        return ''
    }
  }

  const indicatorList = [
    { key: 'rsi', label: 'RSI', emoji: '📊' },
    { key: 'macd', label: 'MACD', emoji: '📈' },
    { key: 'ma', label: '移动平均', emoji: '📉' },
    { key: 'bollinger', label: '布林带', emoji: '🎯' },
    { key: 'volume', label: '成交量', emoji: '📦' },
    { key: 'momentum', label: '动量', emoji: '🚀' },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {indicatorList.map(({ key, label, emoji }) => {
        const indicator = indicators[key as keyof typeof indicators]
        if (!indicator) return null

        return (
          <div
            key={key}
            className={`p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-md ${getSignalColor(indicator.signal, indicator.strength)}`}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{emoji}</span>
                <span className="font-medium text-gray-900">{label}</span>
              </div>
              <div className="flex items-center space-x-1">
                {getStrengthIcon(indicator.strength)}
                {getSignalIcon(indicator.signal)}
              </div>
            </div>

            <div className="mb-2">
              <div className="text-lg font-bold">
                {indicator.value.toFixed(2)}
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <span className="font-medium">
                  {getSignalText(indicator.signal)}
                </span>
                <span className="opacity-75">
                  ({getStrengthText(indicator.strength)})
                </span>
              </div>
            </div>

            <p className="text-xs opacity-80 leading-relaxed">
              {indicator.description}
            </p>
          </div>
        )
      })}
    </div>
  )
}
