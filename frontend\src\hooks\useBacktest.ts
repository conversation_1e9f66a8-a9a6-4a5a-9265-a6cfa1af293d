import { useState } from 'react'
import { backtestAPI } from '../utils/api'

interface BacktestData {
  symbol: string
  strategy: string
  period: string
  performance: {
    total_return: number
    annual_return: number
    sharpe_ratio: number
    max_drawdown: number
    win_rate: number
    profit_factor: number
    total_trades: number
    winning_trades: number
    losing_trades: number
    avg_win: number
    avg_loss: number
  }
  equity_curve: Array<{
    date: string
    portfolio_value: number
    benchmark_value: number
    drawdown: number
  }>
  trades: Array<{
    date: string
    action: string
    price: number
    quantity: number
    commission: number
    portfolio_value: number
  }>
  strategy_description: string
  risk_analysis: any
  generated_at: string
}

export const useBacktest = () => {
  const [backtest, setBacktest] = useState<BacktestData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const runBacktest = async (config: {
    symbol: string
    strategy: string
    start_date: string
    end_date: string
    initial_capital?: number
    commission?: number
  }) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await backtestAPI.runBacktest(config)
      setBacktest(response.data)
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || '回测失败，请检查参数设置'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const quickBacktest = async (symbol: string, strategy: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await backtestAPI.quickBacktest(symbol, strategy)
      setBacktest(response.data)
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || '快速回测失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const clearBacktest = () => {
    setBacktest(null)
    setError(null)
  }

  return {
    backtest,
    loading,
    error,
    runBacktest,
    quickBacktest,
    clearBacktest
  }
}
