# 🚀 智能量化分析系统

专为小白用户设计的专业量化分析平台 - 复杂功能，简单操作

## 🎯 核心特性

### 🤖 AI智能分析
- **一键分析**: 输入股票代码，自动完成全面分析
- **智能解读**: AI给出明确的买卖建议
- **风险评估**: 自动识别投资风险并提醒

### 📊 可视化回测
- **一键回测**: 快速验证策略历史表现
- **图表展示**: 直观的收益曲线和风险指标
- **性能分析**: 自动计算夏普比率、最大回撤等

### 🔮 走势预测
- **机器学习**: 多模型融合预测股价走势
- **置信区间**: 显示预测的可信度
- **实时更新**: 根据最新数据动态调整预测

### 📈 多指标分析
- **技术指标**: MA、MACD、RSI、布林带等
- **智能融合**: 自动整合多个指标信号
- **信号强度**: 用颜色和图标表示信号强弱

## 🎮 使用流程

1. **输入股票代码** → 系统自动获取数据
2. **查看AI分析** → 获得明确投资建议
3. **一键回测** → 验证策略可靠性
4. **跟踪预测** → 监控走势变化

## 🏗️ 项目结构

```
├── frontend/                 # React前端
│   ├── src/
│   │   ├── components/      # 核心组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义钩子
│   │   └── utils/          # 工具函数
├── backend/                 # Python后端
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
├── data/                   # 数据存储
├── docs/                   # 文档
└── docker/                 # Docker配置
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- Docker (可选)

### 安装依赖

```bash
# 前端依赖
cd frontend
npm install

# 后端依赖
cd ../backend
pip install -r requirements.txt
```

### 启动服务

```bash
# 启动后端
cd backend
uvicorn app.main:app --reload

# 启动前端
cd frontend
npm run dev
```

## 📱 界面预览

- **主分析页面**: 一键分析，结果直观
- **预测图表**: 走势预测，置信区间
- **回测报告**: 策略表现，风险指标
- **指标仪表板**: 多指标融合，信号强度

## 🎯 设计理念

**专业功能 + 傻瓜操作**

- ✅ 一键启动：复杂分析一键完成
- ✅ 结果导向：直接给出投资建议
- ✅ 可视化优先：用图表代替数字
- ✅ 智能提醒：自动风险评估

## 📞 技术支持

如有问题，请查看文档或提交Issue。

---

*让量化投资变得简单易懂* 🎯
