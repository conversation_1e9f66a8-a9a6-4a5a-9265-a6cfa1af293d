import yfinance as yf
import pandas as pd
import numpy as np
import ta
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class StockAnalyzer:
    """股票数据分析器"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存
    
    async def get_stock_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取股票数据"""
        try:
            # 检查缓存
            cache_key = f"stock_data:{symbol}"
            if cache_key in self.cache:
                cached_time, data = self.cache[cache_key]
                if (datetime.now() - cached_time).seconds < self.cache_timeout:
                    return data
            
            # 获取股票信息
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # 获取历史数据 (1年)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            hist = ticker.history(start=start_date, end=end_date)
            
            if hist.empty:
                logger.warning(f"未找到股票数据: {symbol}")
                return None
            
            # 获取当前价格和变化
            current_price = hist['Close'].iloc[-1]
            prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
            price_change = current_price - prev_price
            price_change_percent = (price_change / prev_price) * 100
            
            # 准备价格历史数据
            price_history = []
            for date, row in hist.tail(60).iterrows():  # 最近60天
                price_history.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'price': float(row['Close']),
                    'volume': int(row['Volume'])
                })
            
            # 构建结果
            result = {
                'symbol': symbol,
                'company_name': info.get('longName', symbol),
                'current_price': float(current_price),
                'price_change': float(price_change),
                'price_change_percent': float(price_change_percent),
                'volume': int(hist['Volume'].iloc[-1]),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'price_history': price_history,
                'raw_data': hist,
                'timestamp': datetime.now().isoformat()
            }
            
            # 缓存结果
            self.cache[cache_key] = (datetime.now(), result)
            
            logger.info(f"获取股票数据成功: {symbol}")
            return result
            
        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {str(e)}")
            return None
    
    async def calculate_indicators(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算技术指标"""
        try:
            df = stock_data['raw_data'].copy()
            
            # RSI (相对强弱指数)
            rsi = ta.momentum.RSIIndicator(df['Close']).rsi().iloc[-1]
            rsi_signal = self._get_rsi_signal(rsi)
            
            # MACD
            macd_line = ta.trend.MACD(df['Close']).macd().iloc[-1]
            macd_signal_line = ta.trend.MACD(df['Close']).macd_signal().iloc[-1]
            macd_histogram = ta.trend.MACD(df['Close']).macd_diff().iloc[-1]
            macd_signal = self._get_macd_signal(macd_line, macd_signal_line, macd_histogram)
            
            # 移动平均线
            ma_20 = ta.trend.SMAIndicator(df['Close'], window=20).sma_indicator().iloc[-1]
            ma_50 = ta.trend.SMAIndicator(df['Close'], window=50).sma_indicator().iloc[-1]
            current_price = df['Close'].iloc[-1]
            ma_signal = self._get_ma_signal(current_price, ma_20, ma_50)
            
            # 布林带
            bb_upper = ta.volatility.BollingerBands(df['Close']).bollinger_hband().iloc[-1]
            bb_lower = ta.volatility.BollingerBands(df['Close']).bollinger_lband().iloc[-1]
            bb_middle = ta.volatility.BollingerBands(df['Close']).bollinger_mavg().iloc[-1]
            bb_signal = self._get_bollinger_signal(current_price, bb_upper, bb_lower, bb_middle)
            
            # 成交量分析
            volume_sma = ta.volume.VolumeSMAIndicator(df['Close'], df['Volume']).volume_sma().iloc[-1]
            current_volume = df['Volume'].iloc[-1]
            volume_signal = self._get_volume_signal(current_volume, volume_sma)
            
            # 动量指标
            momentum = ta.momentum.ROCIndicator(df['Close']).roc().iloc[-1]
            momentum_signal = self._get_momentum_signal(momentum)
            
            return {
                'rsi': {
                    'name': 'RSI',
                    'value': float(rsi),
                    'signal': rsi_signal['signal'],
                    'strength': rsi_signal['strength'],
                    'description': rsi_signal['description']
                },
                'macd': {
                    'name': 'MACD',
                    'value': float(macd_line),
                    'signal': macd_signal['signal'],
                    'strength': macd_signal['strength'],
                    'description': macd_signal['description']
                },
                'ma': {
                    'name': '移动平均',
                    'value': float(ma_20),
                    'signal': ma_signal['signal'],
                    'strength': ma_signal['strength'],
                    'description': ma_signal['description']
                },
                'bollinger': {
                    'name': '布林带',
                    'value': float(bb_middle),
                    'signal': bb_signal['signal'],
                    'strength': bb_signal['strength'],
                    'description': bb_signal['description']
                },
                'volume': {
                    'name': '成交量',
                    'value': float(current_volume),
                    'signal': volume_signal['signal'],
                    'strength': volume_signal['strength'],
                    'description': volume_signal['description']
                },
                'momentum': {
                    'name': '动量',
                    'value': float(momentum),
                    'signal': momentum_signal['signal'],
                    'strength': momentum_signal['strength'],
                    'description': momentum_signal['description']
                }
            }
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {str(e)}")
            return {}
    
    def _get_rsi_signal(self, rsi: float) -> Dict[str, str]:
        """RSI信号分析"""
        if rsi > 70:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if rsi > 80 else 'MODERATE',
                'description': f'RSI={rsi:.1f}，超买区域，考虑卖出'
            }
        elif rsi < 30:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if rsi < 20 else 'MODERATE',
                'description': f'RSI={rsi:.1f}，超卖区域，考虑买入'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': f'RSI={rsi:.1f}，处于正常区间'
            }
    
    def _get_macd_signal(self, macd_line: float, signal_line: float, histogram: float) -> Dict[str, str]:
        """MACD信号分析"""
        if macd_line > signal_line and histogram > 0:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if histogram > 0.5 else 'MODERATE',
                'description': 'MACD金叉，上涨趋势'
            }
        elif macd_line < signal_line and histogram < 0:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if histogram < -0.5 else 'MODERATE',
                'description': 'MACD死叉，下跌趋势'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'MACD信号不明确'
            }
    
    def _get_ma_signal(self, price: float, ma_20: float, ma_50: float) -> Dict[str, str]:
        """移动平均线信号分析"""
        if price > ma_20 > ma_50:
            return {
                'signal': 'BUY',
                'strength': 'STRONG',
                'description': '价格突破短期均线，上涨趋势'
            }
        elif price < ma_20 < ma_50:
            return {
                'signal': 'SELL',
                'strength': 'STRONG',
                'description': '价格跌破短期均线，下跌趋势'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'MODERATE',
                'description': '价格在均线附近震荡'
            }
    
    def _get_bollinger_signal(self, price: float, upper: float, lower: float, middle: float) -> Dict[str, str]:
        """布林带信号分析"""
        if price > upper:
            return {
                'signal': 'SELL',
                'strength': 'MODERATE',
                'description': '价格突破上轨，可能回调'
            }
        elif price < lower:
            return {
                'signal': 'BUY',
                'strength': 'MODERATE',
                'description': '价格跌破下轨，可能反弹'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '价格在布林带内正常波动'
            }
    
    def _get_volume_signal(self, current_volume: float, avg_volume: float) -> Dict[str, str]:
        """成交量信号分析"""
        volume_ratio = current_volume / avg_volume
        if volume_ratio > 1.5:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if volume_ratio > 2 else 'MODERATE',
                'description': f'成交量放大{volume_ratio:.1f}倍，关注度提升'
            }
        elif volume_ratio < 0.5:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量萎缩，市场观望'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量正常'
            }
    
    def _get_momentum_signal(self, momentum: float) -> Dict[str, str]:
        """动量信号分析"""
        if momentum > 5:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if momentum > 10 else 'MODERATE',
                'description': f'动量强劲({momentum:.1f}%)，上涨趋势'
            }
        elif momentum < -5:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if momentum < -10 else 'MODERATE',
                'description': f'动量疲弱({momentum:.1f}%)，下跌趋势'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '动量平稳'
            }
