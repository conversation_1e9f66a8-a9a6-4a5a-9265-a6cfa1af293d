from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class PredictionPoint(BaseModel):
    """预测点数据模型"""
    date: str = Field(..., description="预测日期")
    predicted_price: float = Field(..., description="预测价格")
    confidence: float = Field(..., ge=0, le=1, description="置信度")
    lower_bound: float = Field(..., description="价格下界")
    upper_bound: float = Field(..., description="价格上界")

class PredictionRequest(BaseModel):
    """预测请求模型"""
    symbol: str = Field(..., description="股票代码")
    prediction_days: int = Field(30, ge=1, le=90, description="预测天数")
    model_type: Optional[str] = Field("ensemble", description="模型类型")
    confidence_threshold: Optional[float] = Field(0.5, ge=0, le=1, description="置信度阈值")
    
    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "prediction_days": 30,
                "model_type": "ensemble",
                "confidence_threshold": 0.7
            }
        }

class PredictionResponse(BaseModel):
    """预测响应模型"""
    symbol: str = Field(..., description="股票代码")
    current_price: float = Field(..., description="当前价格")
    prediction_horizon: int = Field(..., description="预测周期(天)")
    predictions: List[PredictionPoint] = Field(..., description="预测数据点")
    trend_direction: str = Field(..., description="趋势方向: BULLISH, BEARISH, NEUTRAL")
    trend_strength: str = Field(..., description="趋势强度: STRONG, MODERATE, WEAK")
    overall_confidence: float = Field(..., ge=0, le=1, description="整体置信度")
    risk_level: str = Field(..., description="风险等级: LOW, MODERATE, HIGH")
    model_used: str = Field(..., description="使用的模型")
    generated_at: str = Field(..., description="生成时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "current_price": 150.25,
                "prediction_horizon": 30,
                "predictions": [
                    {
                        "date": "2024-01-02",
                        "predicted_price": 152.30,
                        "confidence": 0.85,
                        "lower_bound": 148.50,
                        "upper_bound": 156.10
                    }
                ],
                "trend_direction": "BULLISH",
                "trend_strength": "MODERATE",
                "overall_confidence": 0.78,
                "risk_level": "MODERATE",
                "model_used": "ensemble",
                "generated_at": "2024-01-01T12:00:00Z"
            }
        }
