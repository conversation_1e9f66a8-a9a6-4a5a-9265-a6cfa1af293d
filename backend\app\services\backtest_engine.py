import numpy as np
import pandas as pd
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False

try:
    import ta
    TA_AVAILABLE = True
except ImportError:
    TA_AVAILABLE = False

from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, timedelta

from app.models.backtest import (
    BacktestRequest, BacktestResponse, TradeRecord,
    PerformanceMetrics, EquityCurvePoint
)

logger = logging.getLogger(__name__)

class BacktestEngine:
    """回测引擎"""

    def __init__(self):
        self.strategies = {
            'ma_crossover': self._ma_crossover_strategy,
            'rsi_strategy': self._rsi_strategy,
            'macd_strategy': self._macd_strategy,
            'bollinger_bands': self._bollinger_bands_strategy
        }

    async def run_backtest(self, request: BacktestRequest) -> Optional[BacktestResponse]:
        """运行回测"""
        try:
            logger.info(f"开始回测: {request.symbol}, 策略: {request.strategy}")

            # 获取历史数据
            data = await self._get_historical_data(
                request.symbol,
                request.start_date,
                request.end_date
            )

            if data is None or data.empty:
                logger.warning(f"未找到数据: {request.symbol}")
                return None

            # 执行策略
            if request.strategy not in self.strategies:
                raise ValueError(f"不支持的策略: {request.strategy}")

            signals = self.strategies[request.strategy](data)

            # 模拟交易
            trades, equity_curve = self._simulate_trading(
                data, signals, request.initial_capital, request.commission
            )

            # 计算绩效指标
            performance = self._calculate_performance(trades, equity_curve, data)

            # 风险分析
            risk_analysis = self._analyze_risk(equity_curve, data)

            # 构建响应
            return BacktestResponse(
                symbol=request.symbol,
                strategy=request.strategy,
                period=f"{request.start_date} to {request.end_date}",
                performance=performance,
                equity_curve=equity_curve,
                trades=trades,
                strategy_description=self._get_strategy_description(request.strategy),
                risk_analysis=risk_analysis,
                generated_at=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"回测失败: {str(e)}")
            return None

    async def _get_historical_data(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """获取A股历史数据"""
        try:
            # 使用A股分析器获取历史数据
            from app.services.a_stock_analyzer import AStockAnalyzer
            a_stock_analyzer = AStockAnalyzer()

            # 获取A股数据
            stock_data = await a_stock_analyzer.get_stock_data(symbol)
            if not stock_data or 'raw_data' not in stock_data:
                logger.warning(f"未找到A股数据: {symbol}")
                return None

            data = stock_data['raw_data'].copy()
            if data.empty:
                logger.warning(f"A股历史数据为空: {symbol}")
                return None

            # 标准化列名以适配回测引擎
            if 'close' in data.columns:
                data['Close'] = data['close']
            if 'open' in data.columns:
                data['Open'] = data['open']
            if 'high' in data.columns:
                data['High'] = data['high']
            if 'low' in data.columns:
                data['Low'] = data['low']
            if 'vol' in data.columns:
                data['Volume'] = data['vol']

            # 确保有必要的列
            if 'Close' not in data.columns:
                logger.error(f"数据缺少Close列: {symbol}")
                return None

            # 计算技术指标
            try:
                if TA_AVAILABLE:
                    data['SMA_20'] = ta.trend.SMAIndicator(data['Close'], window=20).sma_indicator()
                    data['SMA_50'] = ta.trend.SMAIndicator(data['Close'], window=50).sma_indicator()
                    data['RSI'] = ta.momentum.RSIIndicator(data['Close']).rsi()

                    # MACD
                    macd = ta.trend.MACD(data['Close'])
                    data['MACD'] = macd.macd()
                    data['MACD_Signal'] = macd.macd_signal()
                    data['MACD_Histogram'] = macd.macd_diff()

                    # 布林带
                    bollinger = ta.volatility.BollingerBands(data['Close'])
                    data['BB_Upper'] = bollinger.bollinger_hband()
                    data['BB_Lower'] = bollinger.bollinger_lband()
                else:
                    # 简化指标计算
                    data['SMA_20'] = data['Close'].rolling(20).mean()
                    data['SMA_50'] = data['Close'].rolling(50).mean()
                    # 简化RSI
                    delta = data['Close'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
                    rs = gain / loss
                    data['RSI'] = 100 - (100 / (1 + rs))

                    # 简化MACD
                    ema_12 = data['Close'].ewm(span=12).mean()
                    ema_26 = data['Close'].ewm(span=26).mean()
                    data['MACD'] = ema_12 - ema_26
                    data['MACD_Signal'] = data['MACD'].ewm(span=9).mean()
                    data['MACD_Histogram'] = data['MACD'] - data['MACD_Signal']

                    # 简化布林带
                    data['BB_Upper'] = data['SMA_20'] + (data['Close'].rolling(20).std() * 2)
                    data['BB_Lower'] = data['SMA_20'] - (data['Close'].rolling(20).std() * 2)
            except Exception as e:
                logger.warning(f"技术指标计算失败: {e}")
                # 使用简单的移动平均
                data['SMA_20'] = data['Close'].rolling(20).mean()
                data['SMA_50'] = data['Close'].rolling(50).mean()
                # 简单布林带
                data['BB_Upper'] = data['SMA_20'] + (data['Close'].rolling(20).std() * 2)
                data['BB_Lower'] = data['SMA_20'] - (data['Close'].rolling(20).std() * 2)

            # 添加布林带中线
            if 'BB_Upper' in data.columns and 'BB_Lower' in data.columns:
                data['BB_Middle'] = (data['BB_Upper'] + data['BB_Lower']) / 2

            return data

        except Exception as e:
            logger.error(f"获取历史数据失败: {str(e)}")
            return None

    def _ma_crossover_strategy(self, data: pd.DataFrame) -> pd.Series:
        """移动平均交叉策略"""
        signals = pd.Series(0, index=data.index)

        # 金叉买入，死叉卖出
        golden_cross = (data['SMA_20'] > data['SMA_50']) & (data['SMA_20'].shift(1) <= data['SMA_50'].shift(1))
        death_cross = (data['SMA_20'] < data['SMA_50']) & (data['SMA_20'].shift(1) >= data['SMA_50'].shift(1))

        signals[golden_cross] = 1  # 买入信号
        signals[death_cross] = -1  # 卖出信号

        return signals

    def _rsi_strategy(self, data: pd.DataFrame) -> pd.Series:
        """RSI策略"""
        signals = pd.Series(0, index=data.index)

        # RSI超卖买入，超买卖出
        oversold = (data['RSI'] < 30) & (data['RSI'].shift(1) >= 30)
        overbought = (data['RSI'] > 70) & (data['RSI'].shift(1) <= 70)

        signals[oversold] = 1  # 买入信号
        signals[overbought] = -1  # 卖出信号

        return signals

    def _macd_strategy(self, data: pd.DataFrame) -> pd.Series:
        """MACD策略"""
        signals = pd.Series(0, index=data.index)

        # MACD金叉买入，死叉卖出
        golden_cross = (data['MACD'] > data['MACD_Signal']) & (data['MACD'].shift(1) <= data['MACD_Signal'].shift(1))
        death_cross = (data['MACD'] < data['MACD_Signal']) & (data['MACD'].shift(1) >= data['MACD_Signal'].shift(1))

        signals[golden_cross] = 1  # 买入信号
        signals[death_cross] = -1  # 卖出信号

        return signals

    def _bollinger_bands_strategy(self, data: pd.DataFrame) -> pd.Series:
        """布林带策略"""
        signals = pd.Series(0, index=data.index)

        # 价格触及下轨买入，触及上轨卖出
        touch_lower = (data['Close'] <= data['BB_Lower']) & (data['Close'].shift(1) > data['BB_Lower'].shift(1))
        touch_upper = (data['Close'] >= data['BB_Upper']) & (data['Close'].shift(1) < data['BB_Upper'].shift(1))

        signals[touch_lower] = 1  # 买入信号
        signals[touch_upper] = -1  # 卖出信号

        return signals

    def _simulate_trading(self, data: pd.DataFrame, signals: pd.Series, initial_capital: float, commission: float):
        """模拟交易"""
        trades = []
        equity_curve = []

        cash = initial_capital
        shares = 0
        portfolio_value = initial_capital

        for date, signal in signals.items():
            if pd.isna(signal) or signal == 0:
                continue

            price = data.loc[date, 'Close']

            if signal == 1 and cash > 0:  # 买入
                # 全仓买入
                quantity = int(cash / price)
                if quantity > 0:
                    cost = quantity * price * (1 + commission)
                    if cost <= cash:
                        cash -= cost
                        shares += quantity

                        # 处理日期格式
                        if hasattr(date, 'strftime'):
                            date_str = date.strftime('%Y-%m-%d')
                        else:
                            date_str = str(date)

                        trades.append(TradeRecord(
                            date=date_str,
                            action='BUY',
                            price=float(price),
                            quantity=quantity,
                            commission=float(quantity * price * commission),
                            portfolio_value=float(cash + shares * price)
                        ))

            elif signal == -1 and shares > 0:  # 卖出
                # 全部卖出
                proceeds = shares * price * (1 - commission)
                cash += proceeds

                # 处理日期格式
                if hasattr(date, 'strftime'):
                    date_str = date.strftime('%Y-%m-%d')
                else:
                    date_str = str(date)

                trades.append(TradeRecord(
                    date=date_str,
                    action='SELL',
                    price=float(price),
                    quantity=shares,
                    commission=float(shares * price * commission),
                    portfolio_value=float(cash)
                ))

                shares = 0

            # 记录权益曲线
            current_value = cash + shares * price
            benchmark_value = initial_capital * (price / data['Close'].iloc[0])

            # 处理日期格式
            if hasattr(date, 'strftime'):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = str(date)

            equity_curve.append(EquityCurvePoint(
                date=date_str,
                portfolio_value=float(current_value),
                benchmark_value=float(benchmark_value),
                drawdown=float((current_value - portfolio_value) / portfolio_value * 100) if portfolio_value > 0 else 0
            ))

            portfolio_value = current_value

        return trades, equity_curve

    def _calculate_performance(self, trades: List[TradeRecord], equity_curve: List[EquityCurvePoint], data: pd.DataFrame) -> PerformanceMetrics:
        """计算绩效指标"""
        if not trades or not equity_curve:
            return self._get_default_performance()

        # 计算收益
        initial_value = equity_curve[0].portfolio_value
        final_value = equity_curve[-1].portfolio_value
        total_return = (final_value - initial_value) / initial_value * 100

        # 计算年化收益
        days = len(equity_curve)
        annual_return = ((final_value / initial_value) ** (365 / days) - 1) * 100

        # 计算最大回撤
        max_drawdown = min([point.drawdown for point in equity_curve])

        # 交易统计
        buy_trades = [t for t in trades if t.action == 'BUY']
        sell_trades = [t for t in trades if t.action == 'SELL']

        total_trades = len(buy_trades)

        # 计算盈亏
        if len(buy_trades) == len(sell_trades):
            profits = []
            for i in range(len(buy_trades)):
                buy_price = buy_trades[i].price
                sell_price = sell_trades[i].price
                profit = (sell_price - buy_price) / buy_price * 100
                profits.append(profit)

            winning_trades = len([p for p in profits if p > 0])
            losing_trades = len([p for p in profits if p <= 0])
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

            avg_win = np.mean([p for p in profits if p > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([p for p in profits if p <= 0]) if losing_trades > 0 else 0

            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        else:
            winning_trades = 0
            losing_trades = 0
            win_rate = 0
            avg_win = 0
            avg_loss = 0
            profit_factor = 0

        # 夏普比率 (简化计算)
        returns = [
            (equity_curve[i].portfolio_value - equity_curve[i-1].portfolio_value) / equity_curve[i-1].portfolio_value
            for i in range(1, len(equity_curve))
        ]
        sharpe_ratio = (np.mean(returns) / np.std(returns) * np.sqrt(252)) if len(returns) > 1 and np.std(returns) > 0 else 0

        return PerformanceMetrics(
            total_return=float(total_return),
            annual_return=float(annual_return),
            sharpe_ratio=float(sharpe_ratio),
            max_drawdown=float(max_drawdown),
            win_rate=float(win_rate),
            profit_factor=float(profit_factor),
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=float(avg_win),
            avg_loss=float(avg_loss)
        )

    def _analyze_risk(self, equity_curve: List[EquityCurvePoint], data: pd.DataFrame) -> Dict[str, Any]:
        """风险分析"""
        if not equity_curve:
            return {}

        values = [point.portfolio_value for point in equity_curve]
        returns = [(values[i] - values[i-1]) / values[i-1] for i in range(1, len(values))]

        return {
            'volatility': float(np.std(returns) * np.sqrt(252)) if len(returns) > 1 else 0,
            'var_95': float(np.percentile(returns, 5)) if returns else 0,
            'skewness': float(pd.Series(returns).skew()) if len(returns) > 2 else 0,
            'kurtosis': float(pd.Series(returns).kurtosis()) if len(returns) > 3 else 0
        }

    def _get_strategy_description(self, strategy: str) -> str:
        """获取策略描述"""
        descriptions = {
            'ma_crossover': '移动平均交叉策略：基于20日和50日移动平均线的金叉死叉信号',
            'rsi_strategy': 'RSI策略：基于相对强弱指数的超买超卖信号',
            'macd_strategy': 'MACD策略：基于MACD指标的趋势跟踪信号',
            'bollinger_bands': '布林带策略：基于布林带上下轨的均值回归信号'
        }
        return descriptions.get(strategy, '未知策略')

    def _get_default_performance(self) -> PerformanceMetrics:
        """获取默认绩效指标"""
        return PerformanceMetrics(
            total_return=0,
            annual_return=0,
            sharpe_ratio=0,
            max_drawdown=0,
            win_rate=0,
            profit_factor=0,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            avg_win=0,
            avg_loss=0
        )

    async def get_available_strategies(self) -> List[Dict[str, Any]]:
        """获取可用策略"""
        return [
            {
                'name': 'ma_crossover',
                'display_name': '移动平均交叉',
                'description': '基于短期和长期移动平均线的交叉信号',
                'risk_level': 'MODERATE',
                'best_for': '趋势市场'
            },
            {
                'name': 'rsi_strategy',
                'display_name': 'RSI策略',
                'description': '基于相对强弱指数的超买超卖策略',
                'risk_level': 'MODERATE',
                'best_for': '震荡市场'
            },
            {
                'name': 'macd_strategy',
                'display_name': 'MACD策略',
                'description': '基于MACD指标的趋势跟踪策略',
                'risk_level': 'MODERATE',
                'best_for': '趋势确认'
            },
            {
                'name': 'bollinger_bands',
                'display_name': '布林带策略',
                'description': '基于布林带的均值回归策略',
                'risk_level': 'LOW',
                'best_for': '区间震荡'
            }
        ]

    async def compare_strategies(self, symbol: str) -> Dict[str, Any]:
        """对比策略表现"""
        # 这里应该运行多个策略并对比结果
        # 目前返回模拟数据
        return {
            'ma_crossover': {'return': 15.6, 'sharpe': 1.23, 'max_dd': -8.5},
            'rsi_strategy': {'return': 12.3, 'sharpe': 1.05, 'max_dd': -6.2},
            'macd_strategy': {'return': 18.9, 'sharpe': 1.45, 'max_dd': -12.1},
            'bollinger_bands': {'return': 9.7, 'sharpe': 0.89, 'max_dd': -4.8}
        }
