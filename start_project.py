#!/usr/bin/env python3
"""
A股智能量化分析系统启动脚本
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 A股智能量化分析系统")
    print("=" * 60)
    print("📊 专业功能 · 傻瓜操作")
    print("🤖 AI分析 · 走势预测 · 策略回测")
    print("🇨🇳 专为A股市场优化")
    print("=" * 60)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查依赖是否安装"""
    print("\n📦 检查依赖...")
    
    # 检查后端依赖
    backend_deps = [
        'fastapi', 'uvicorn', 'pandas', 'numpy', 
        'tushare', 'requests', 'pydantic'
    ]
    
    missing_deps = []
    for dep in backend_deps:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} (缺失)")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n⚠️  缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install -r backend/requirements.txt")
        return False
    
    return True

def start_backend():
    """启动后端服务"""
    print("\n🔧 启动后端服务...")
    
    # 切换到backend目录
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ 错误: backend目录不存在")
        return None
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = str(backend_dir.absolute())
    
    try:
        # 启动FastAPI服务
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print(f"工作目录: {backend_dir.absolute()}")
        
        process = subprocess.Popen(
            cmd,
            cwd=backend_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("✅ 后端服务启动中...")
        print("📡 API地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        
        return process
        
    except Exception as e:
        print(f"❌ 启动后端失败: {e}")
        return None

def wait_for_backend():
    """等待后端服务启动"""
    print("\n⏳ 等待后端服务启动...")
    
    import requests
    max_attempts = 30
    for i in range(max_attempts):
        try:
            response = requests.get("http://localhost:8000/health", timeout=2)
            if response.status_code == 200:
                print("✅ 后端服务已就绪")
                return True
        except:
            pass
        
        print(f"   尝试 {i+1}/{max_attempts}...")
        time.sleep(2)
    
    print("❌ 后端服务启动超时")
    return False

def open_frontend():
    """打开前端页面"""
    print("\n🌐 打开前端页面...")
    
    # 检查simple.html是否存在
    simple_html = Path("frontend/simple.html")
    if simple_html.exists():
        frontend_url = f"file:///{simple_html.absolute()}"
        print(f"📱 Simple页面: {frontend_url}")
        
        try:
            webbrowser.open(frontend_url)
            print("✅ 已在浏览器中打开Simple页面")
        except Exception as e:
            print(f"⚠️  无法自动打开浏览器: {e}")
            print(f"请手动打开: {frontend_url}")
    else:
        print("❌ frontend/simple.html 不存在")

def start_react_frontend():
    """启动React前端 (可选)"""
    print("\n⚛️  启动React前端 (可选)...")
    
    frontend_dir = Path("frontend")
    package_json = frontend_dir / "package.json"
    
    if not package_json.exists():
        print("⚠️  React前端未配置，跳过")
        return None
    
    try:
        # 检查node_modules
        node_modules = frontend_dir / "node_modules"
        if not node_modules.exists():
            print("📦 安装前端依赖...")
            subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
        
        # 启动开发服务器
        print("🚀 启动React开发服务器...")
        process = subprocess.Popen(
            ["npm", "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ React前端启动中...")
        print("🌐 React地址: http://localhost:3000")
        
        return process
        
    except subprocess.CalledProcessError as e:
        print(f"❌ React前端启动失败: {e}")
        return None
    except FileNotFoundError:
        print("⚠️  npm未安装，跳过React前端")
        return None

def show_usage_info():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("🎯 使用说明")
    print("=" * 60)
    print("📱 Simple页面 (推荐新手):")
    print("   - 直接在浏览器中使用")
    print("   - 输入A股代码 (如: 000001, 600519)")
    print("   - 点击按钮进行分析、预测、回测")
    print("")
    print("⚛️  React页面 (高级用户):")
    print("   - 访问 http://localhost:3000")
    print("   - 更丰富的交互界面")
    print("   - 支持更多自定义选项")
    print("")
    print("📚 API文档:")
    print("   - 访问 http://localhost:8000/docs")
    print("   - 查看所有API接口")
    print("   - 在线测试API功能")
    print("")
    print("🔧 功能特色:")
    print("   🤖 AI智能分析 - 一键获取买卖建议")
    print("   🔮 走势预测 - 机器学习预测股价")
    print("   📊 策略回测 - 验证交易策略效果")
    print("   📈 技术指标 - 多种专业指标分析")
    print("")
    print("⚠️  注意事项:")
    print("   - 首次使用需要下载A股数据")
    print("   - 预测结果仅供参考，投资有风险")
    print("   - 建议结合多种分析方法")
    print("=" * 60)

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查依赖
    if not check_dependencies():
        print("\n💡 安装依赖:")
        print("   cd backend")
        print("   pip install -r requirements.txt")
        return 1
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        return 1
    
    try:
        # 等待后端启动
        if not wait_for_backend():
            return 1
        
        # 打开前端页面
        open_frontend()
        
        # 启动React前端 (可选)
        react_process = start_react_frontend()
        
        # 显示使用说明
        show_usage_info()
        
        print("\n🎉 系统启动完成！")
        print("按 Ctrl+C 停止服务")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n📴 正在关闭服务...")
            
    finally:
        # 清理进程
        if backend_process:
            backend_process.terminate()
            backend_process.wait()
        
        print("✅ 服务已停止")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
