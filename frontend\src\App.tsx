import { Routes, Route } from 'react-router-dom'
import { Layout } from './components/Layout'
import { HomePage } from './pages/HomePage'
import { AnalysisPage } from './pages/AnalysisPage'
import { BacktestPage } from './pages/BacktestPage'
import { PredictionPage } from './pages/PredictionPage'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/analysis" element={<AnalysisPage />} />
        <Route path="/analysis/:symbol" element={<AnalysisPage />} />
        <Route path="/backtest" element={<BacktestPage />} />
        <Route path="/prediction" element={<PredictionPage />} />
        <Route path="/prediction/:symbol" element={<PredictionPage />} />
      </Routes>
    </Layout>
  )
}

export default App
