#!/usr/bin/env python3
"""
测试修复后的暗黑主题UI功能
"""

import requests
import json
import webbrowser
from pathlib import Path

def test_api_field_mapping():
    """测试API字段映射"""
    print("🔍 测试API字段映射")
    print("="*50)
    
    base_url = "http://localhost:8000"
    test_symbol = "000001"
    
    # 测试AI分析API
    print("\n📊 测试AI分析API字段映射...")
    try:
        response = requests.get(f"{base_url}/api/analyze/{test_symbol}", timeout=30)
        if response.status_code == 200:
            data = response.json()
            print("✅ AI分析API正常")
            
            # 检查关键字段
            expected_fields = ['symbol', 'current_price', 'summary', 'recommendation', 'confidence']
            found_fields = [f for f in expected_fields if f in data]
            missing_fields = [f for f in expected_fields if f not in data]
            
            print(f"   📋 找到字段: {found_fields}")
            if missing_fields:
                print(f"   ⚠️  缺少字段: {missing_fields}")
            
            # 显示实际数据结构
            print(f"   📊 实际字段: {list(data.keys())}")
            
        else:
            print(f"❌ AI分析API错误: {response.status_code}")
    except Exception as e:
        print(f"❌ AI分析API异常: {e}")
    
    # 测试预测API
    print("\n🔮 测试预测API字段映射...")
    try:
        response = requests.get(f"{base_url}/api/predict/{test_symbol}", timeout=30)
        if response.status_code == 200:
            data = response.json()
            print("✅ 预测API正常")
            
            # 检查关键字段
            expected_fields = ['symbol', 'current_price', 'predictions', 'overall_confidence']
            found_fields = [f for f in expected_fields if f in data]
            missing_fields = [f for f in expected_fields if f not in data]
            
            print(f"   📋 找到字段: {found_fields}")
            if missing_fields:
                print(f"   ⚠️  缺少字段: {missing_fields}")
            
            print(f"   📊 实际字段: {list(data.keys())}")
            
        else:
            print(f"❌ 预测API错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 预测API异常: {e}")
    
    # 测试回测API
    print("\n📈 测试回测API字段映射...")
    try:
        response = requests.get(f"{base_url}/api/backtest/{test_symbol}/ma_crossover", timeout=30)
        if response.status_code == 200:
            data = response.json()
            print("✅ 回测API正常")
            
            # 检查关键字段
            expected_fields = ['symbol', 'strategy', 'performance', 'trades']
            found_fields = [f for f in expected_fields if f in data]
            missing_fields = [f for f in expected_fields if f not in data]
            
            print(f"   📋 找到字段: {found_fields}")
            if missing_fields:
                print(f"   ⚠️  缺少字段: {missing_fields}")
            
            print(f"   📊 实际字段: {list(data.keys())}")
            
        else:
            print(f"❌ 回测API错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 回测API异常: {e}")
    
    # 测试多指标分析API
    print("\n📊 测试多指标分析API字段映射...")
    try:
        response = requests.get(f"{base_url}/api/multi-indicators/{test_symbol}", timeout=30)
        if response.status_code == 200:
            data = response.json()
            print("✅ 多指标分析API正常")
            
            # 检查关键字段
            expected_fields = ['symbol', 'detailed_indicators', 'overall_score']
            found_fields = [f for f in expected_fields if f in data]
            missing_fields = [f for f in expected_fields if f not in data]
            
            print(f"   📋 找到字段: {found_fields}")
            if missing_fields:
                print(f"   ⚠️  缺少字段: {missing_fields}")
            
            print(f"   📊 指标数量: {len(data.get('detailed_indicators', {}))}")
            print(f"   🎯 综合评分: {data.get('overall_score', {}).get('overall_score', 'N/A')}")
            
        else:
            print(f"❌ 多指标分析API错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 多指标分析API异常: {e}")

def test_ui_functionality():
    """测试UI功能"""
    print("\n🌐 测试UI功能")
    print("="*50)
    
    # 获取暗黑主题文件路径
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    
    if not dark_html_path.exists():
        print(f"❌ 找不到暗黑主题文件: {dark_html_path}")
        return False
    
    print(f"✅ 找到暗黑主题文件: {dark_html_path}")
    
    # 检查文件内容
    try:
        with open(dark_html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复的字段映射
        fixes_to_check = [
            ('AI分析字段修复', 'data.summary' in content),
            ('预测字段修复', 'data.overall_confidence' in content),
            ('回测字段修复', 'data.performance' in content),
            ('多指标字段修复', 'data.detailed_indicators' in content),
            ('错误处理', 'data.risk_warnings' in content),
            ('空值处理', '|| \'N/A\'' in content)
        ]
        
        print("📋 字段映射修复检查:")
        all_fixed = True
        for name, check in fixes_to_check:
            if check:
                print(f"   ✅ {name}: 已修复")
            else:
                print(f"   ❌ {name}: 未修复")
                all_fixed = False
        
        return all_fixed
        
    except Exception as e:
        print(f"❌ 检查文件内容失败: {e}")
        return False

def open_dark_ui():
    """打开暗黑主题UI"""
    print("\n🚀 打开暗黑主题UI")
    print("="*50)
    
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    
    if dark_html_path.exists():
        file_url = f"file:///{dark_html_path.absolute().as_posix()}"
        print(f"🌐 打开URL: {file_url}")
        
        try:
            webbrowser.open(file_url)
            print("✅ 浏览器已打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return False
    else:
        print(f"❌ 文件不存在: {dark_html_path}")
        return False

def main():
    """主函数"""
    print("🔧 暗黑主题UI功能修复验证")
    print("="*60)
    
    # 测试API字段映射
    test_api_field_mapping()
    
    # 测试UI功能
    ui_ok = test_ui_functionality()
    
    # 打开暗黑主题UI
    browser_ok = open_dark_ui()
    
    # 总结
    print("\n" + "="*60)
    print("📋 修复验证总结")
    print("="*60)
    
    print("✅ 已修复的问题:")
    print("   • AI分析API字段映射 (summary, recommendation, confidence)")
    print("   • 预测API字段映射 (predictions, overall_confidence)")
    print("   • 回测API字段映射 (performance, trades, risk_analysis)")
    print("   • 多指标分析API字段映射 (保持原有正确映射)")
    print("   • 空值和错误处理")
    print("   • 数据结构适配")
    
    print(f"\n🌐 UI状态:")
    print(f"   {'✅' if ui_ok else '❌'} 字段映射修复: {'完成' if ui_ok else '未完成'}")
    print(f"   {'✅' if browser_ok else '❌'} 浏览器打开: {'成功' if browser_ok else '失败'}")
    
    print("\n🧪 测试建议:")
    print("   1. 在浏览器中输入股票代码 (如: 000001)")
    print("   2. 依次点击四个分析按钮:")
    print("      • AI分析 - 查看智能分析和投资建议")
    print("      • 走势预测 - 查看价格预测和趋势分析")
    print("      • 策略回测 - 查看回测绩效和交易记录")
    print("      • 多指标分析 - 查看综合技术指标分析")
    print("   3. 检查数据是否正确显示")
    print("   4. 验证暗黑主题的视觉效果")
    
    print("\n💡 如果仍有问题:")
    print("   • 检查浏览器控制台错误信息")
    print("   • 确认后端服务正常运行")
    print("   • 验证网络连接")
    print("   • 清除浏览器缓存")
    
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    file_url = f"file:///{dark_html_path.absolute().as_posix()}"
    print(f"\n🔗 暗黑主题页面: {file_url}")
    
    print("\n🎉 暗黑主题UI功能修复完成!")
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
