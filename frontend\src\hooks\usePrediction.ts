import { useState } from 'react'
import { predictionAPI } from '../utils/api'

interface PredictionData {
  symbol: string
  current_price: number
  prediction_horizon: number
  predictions: Array<{
    date: string
    predicted_price: number
    confidence: number
    lower_bound: number
    upper_bound: number
  }>
  trend_direction: string
  trend_strength: string
  overall_confidence: number
  risk_level: string
  model_used: string
  generated_at: string
}

export const usePrediction = () => {
  const [prediction, setPrediction] = useState<PredictionData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const predictStock = async (symbol: string, days: number = 30) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await predictionAPI.predictPrice(symbol, days)
      setPrediction(response.data)
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || '预测失败，请检查股票代码是否正确'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const predictWithConfig = async (config: any) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await predictionAPI.predictWithConfig(config)
      setPrediction(response.data)
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || '自定义预测失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const clearPrediction = () => {
    setPrediction(null)
    setError(null)
  }

  return {
    prediction,
    loading,
    error,
    predictStock,
    predictWithConfig,
    clearPrediction
  }
}
