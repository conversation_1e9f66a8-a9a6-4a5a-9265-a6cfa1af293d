import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, timedelta
import yfinance as yf
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import random

from app.models.prediction import PredictionResponse, PredictionRequest, PredictionPoint

logger = logging.getLogger(__name__)

class PredictionEngine:
    """股价预测引擎"""

    def __init__(self):
        self.models = {}
        self.scalers = {}
        self._initialize_models()

    def _initialize_models(self):
        """初始化预测模型"""
        try:
            # 这里应该加载预训练的模型
            # 目前使用简单的随机森林作为示例
            self.models['random_forest'] = RandomForestRegressor(n_estimators=100, random_state=42)
            self.scalers['random_forest'] = StandardScaler()
            logger.info("预测模型初始化完成")
        except Exception as e:
            logger.error(f"模型初始化失败: {str(e)}")

    async def predict_price(self, symbol: str, days: int = 30) -> Optional[PredictionResponse]:
        """预测股价"""
        try:
            # 使用A股分析器获取历史数据
            from app.services.a_stock_analyzer import AStockAnalyzer
            a_stock_analyzer = AStockAnalyzer()

            # 获取A股数据
            stock_data = await a_stock_analyzer.get_stock_data(symbol)
            if not stock_data or 'raw_data' not in stock_data:
                logger.warning(f"未找到A股数据: {symbol}")
                return None

            hist = stock_data['raw_data']
            if hist.empty:
                logger.warning(f"A股历史数据为空: {symbol}")
                return None

            # 准备特征数据
            features = self._prepare_features(hist)

            # 获取当前价格
            current_price = float(hist['close'].iloc[-1])

            # 生成预测
            predictions = self._generate_predictions(features, days, current_price)

            # 分析趋势
            trend_analysis = self._analyze_trend(predictions)

            # 计算置信度
            confidence_metrics = self._calculate_confidence(hist, predictions)

            # 构建响应

            prediction_points = []
            end_date = datetime.now()  # 添加end_date定义
            for i, pred_price in enumerate(predictions):
                future_date = end_date + timedelta(days=i+1)
                confidence = max(0.3, 0.9 - (i * 0.02))  # 随时间递减的置信度

                prediction_points.append(PredictionPoint(
                    date=future_date.strftime('%Y-%m-%d'),
                    predicted_price=float(pred_price),
                    confidence=confidence,
                    lower_bound=float(pred_price * 0.95),
                    upper_bound=float(pred_price * 1.05)
                ))

            return PredictionResponse(
                symbol=symbol,
                current_price=current_price,
                prediction_horizon=days,
                predictions=prediction_points,
                trend_direction=trend_analysis['direction'],
                trend_strength=trend_analysis['strength'],
                overall_confidence=confidence_metrics['overall'],
                risk_level=self._assess_risk_level(predictions, current_price),
                model_used="ensemble",
                generated_at=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"预测失败 {symbol}: {str(e)}")
            return None

    async def predict_with_config(self, request: PredictionRequest) -> Optional[PredictionResponse]:
        """使用自定义配置预测"""
        try:
            # 基础预测
            base_prediction = await self.predict_price(request.symbol, request.prediction_days)

            if not base_prediction:
                return None

            # 根据配置调整预测
            if request.model_type and request.model_type != "ensemble":
                # 这里可以使用特定模型
                pass

            if request.confidence_threshold:
                # 过滤低置信度的预测
                base_prediction.predictions = [
                    p for p in base_prediction.predictions
                    if p.confidence >= request.confidence_threshold
                ]

            return base_prediction

        except Exception as e:
            logger.error(f"自定义预测失败: {str(e)}")
            return None

    def _prepare_features(self, hist: pd.DataFrame) -> np.ndarray:
        """准备特征数据"""
        try:
            # 计算技术指标作为特征
            features = []

            # 价格特征
            features.append(hist['close'].pct_change().fillna(0).values)
            if 'vol' in hist.columns:
                features.append(hist['vol'].pct_change().fillna(0).values)
            else:
                features.append(np.zeros(len(hist)))

            # 移动平均
            ma_5 = hist['close'].rolling(5).mean().bfill().values
            ma_20 = hist['close'].rolling(20).mean().bfill().values
            features.append((hist['close'] / ma_5).fillna(1).values)
            features.append((hist['close'] / ma_20).fillna(1).values)

            # RSI (简化版)
            delta = hist['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            features.append(rsi.fillna(50).values)

            # 转置并返回最近的数据
            feature_matrix = np.array(features).T
            return feature_matrix[-60:]  # 最近60天的数据

        except Exception as e:
            logger.error(f"特征准备失败: {str(e)}")
            return np.array([])

    def _generate_predictions(self, features: np.ndarray, days: int, current_price: float) -> List[float]:
        """生成预测价格"""
        try:
            if len(features) == 0:
                return []

            # 这里应该使用训练好的模型进行预测
            # 目前使用简单的趋势外推 + 随机波动

            # 使用实际的当前价格作为基准
            predictions = []
            price = current_price

            # 从特征中计算趋势（使用价格变化率）
            if features.shape[1] > 0 and len(features) >= 5:
                # 获取最近的价格变化率
                recent_changes = features[-5:, 0]  # 最近5天的价格变化率
                avg_change = np.mean(recent_changes)
                # 限制变化幅度，避免极端值
                avg_change = np.clip(avg_change, -0.1, 0.1)
            else:
                avg_change = 0

            for day in range(days):
                # 基于历史趋势 + 随机波动
                daily_change = avg_change * 0.3 + random.gauss(0, 0.01)
                # 限制单日变化幅度
                daily_change = np.clip(daily_change, -0.1, 0.1)

                price *= (1 + daily_change)
                # 确保价格为正数
                price = max(price, current_price * 0.5)
                predictions.append(price)

            return predictions

        except Exception as e:
            logger.error(f"预测生成失败: {str(e)}")
            return []

    def _analyze_trend(self, predictions: List[float]) -> Dict[str, str]:
        """分析趋势"""
        if len(predictions) < 2:
            return {'direction': 'NEUTRAL', 'strength': 'WEAK'}

        start_price = predictions[0]
        end_price = predictions[-1]
        change_percent = (end_price - start_price) / start_price * 100

        if change_percent > 5:
            direction = 'BULLISH'
            strength = 'STRONG' if change_percent > 10 else 'MODERATE'
        elif change_percent < -5:
            direction = 'BEARISH'
            strength = 'STRONG' if change_percent < -10 else 'MODERATE'
        else:
            direction = 'NEUTRAL'
            strength = 'WEAK'

        return {'direction': direction, 'strength': strength}

    def _calculate_confidence(self, hist: pd.DataFrame, predictions: List[float]) -> Dict[str, float]:
        """计算置信度指标"""
        try:
            # 基于历史波动率计算置信度
            returns = hist['close'].pct_change().dropna()
            volatility = returns.std()

            # 波动率越低，置信度越高
            base_confidence = max(0.3, 1 - volatility * 10)

            return {
                'overall': base_confidence,
                'volatility': float(volatility),
                'data_quality': 0.8  # 假设数据质量良好
            }

        except Exception as e:
            logger.error(f"置信度计算失败: {str(e)}")
            return {'overall': 0.5, 'volatility': 0.1, 'data_quality': 0.5}

    def _assess_risk_level(self, predictions: List[float], current_price: float) -> str:
        """评估风险等级"""
        if len(predictions) == 0:
            return 'HIGH'

        # 计算预测的波动性
        pred_returns = [
            (predictions[i] - predictions[i-1]) / predictions[i-1]
            for i in range(1, len(predictions))
        ]

        if len(pred_returns) == 0:
            return 'MODERATE'

        volatility = np.std(pred_returns)

        if volatility > 0.05:
            return 'HIGH'
        elif volatility > 0.02:
            return 'MODERATE'
        else:
            return 'LOW'

    async def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        return [
            {
                'name': 'ensemble',
                'description': '集成学习模型',
                'accuracy': 0.75,
                'best_for': '中长期预测'
            },
            {
                'name': 'lstm',
                'description': 'LSTM神经网络',
                'accuracy': 0.72,
                'best_for': '短期预测'
            },
            {
                'name': 'random_forest',
                'description': '随机森林',
                'accuracy': 0.68,
                'best_for': '稳定预测'
            }
        ]

    async def get_confidence_metrics(self, symbol: str) -> Dict[str, Any]:
        """获取置信度指标"""
        try:
            # 这里应该基于历史预测准确率计算
            return {
                'historical_accuracy': random.uniform(0.6, 0.8),
                'data_completeness': random.uniform(0.8, 0.95),
                'market_stability': random.uniform(0.5, 0.9),
                'model_confidence': random.uniform(0.7, 0.85)
            }
        except Exception as e:
            logger.error(f"获取置信度指标失败: {str(e)}")
            return {}
