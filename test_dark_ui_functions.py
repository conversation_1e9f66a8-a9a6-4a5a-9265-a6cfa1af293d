#!/usr/bin/env python3
"""
测试暗黑主题UI的功能
"""

import requests
import json
import time

def test_backend_apis():
    """测试后端API是否正常"""
    print("🔍 测试后端API功能")
    print("="*50)

    base_url = "http://localhost:8000"
    test_symbol = "000001"

    apis_to_test = [
        {
            "name": "健康检查",
            "url": f"{base_url}/health",
            "method": "GET"
        },
        {
            "name": "AI分析",
            "url": f"{base_url}/api/analyze/{test_symbol}",
            "method": "GET"
        },
        {
            "name": "走势预测",
            "url": f"{base_url}/api/predict/{test_symbol}",
            "method": "GET"
        },
        {
            "name": "策略回测",
            "url": f"{base_url}/api/backtest/{test_symbol}/ma_crossover",
            "method": "GET"
        },
        {
            "name": "多指标分析",
            "url": f"{base_url}/api/multi-indicators/{test_symbol}",
            "method": "GET"
        }
    ]

    results = {}

    for api in apis_to_test:
        print(f"\n🧪 测试 {api['name']}...")
        try:
            response = requests.get(api['url'], timeout=30)
            status = response.status_code

            if status == 200:
                print(f"   ✅ {api['name']}: 正常 (200)")
                data = response.json()

                # 检查关键字段
                if api['name'] == "AI分析":
                    required_fields = ['symbol', 'current_price', 'ai_analysis', 'investment_advice']
                    missing = [f for f in required_fields if f not in data]
                    if missing:
                        print(f"   ⚠️  缺少字段: {missing}")
                    else:
                        print(f"   ✅ 数据结构完整")

                elif api['name'] == "多指标分析":
                    required_fields = ['symbol', 'detailed_indicators', 'overall_score']
                    missing = [f for f in required_fields if f not in data]
                    if missing:
                        print(f"   ⚠️  缺少字段: {missing}")
                    else:
                        print(f"   ✅ 数据结构完整")
                        print(f"   📊 指标数量: {len(data.get('detailed_indicators', {}))}")
                        print(f"   🎯 综合评分: {data.get('overall_score', {}).get('overall_score', 'N/A')}")

                results[api['name']] = {'status': 'success', 'code': status}
            else:
                print(f"   ❌ {api['name']}: 错误 ({status})")
                results[api['name']] = {'status': 'error', 'code': status}

        except requests.exceptions.Timeout:
            print(f"   ⏰ {api['name']}: 超时")
            results[api['name']] = {'status': 'timeout', 'code': 'timeout'}
        except requests.exceptions.ConnectionError:
            print(f"   🔌 {api['name']}: 连接失败")
            results[api['name']] = {'status': 'connection_error', 'code': 'connection_error'}
        except Exception as e:
            print(f"   ❌ {api['name']}: 异常 - {str(e)}")
            results[api['name']] = {'status': 'exception', 'code': str(e)}

    return results

def check_html_structure():
    """检查HTML文件结构"""
    print("\n🔍 检查HTML文件结构")
    print("="*50)

    try:
        with open('frontend/dark.html', 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查关键元素
        checks = [
            ('axios库', 'axios' in content),
            ('stockSymbol输入框', 'id="stockSymbol"' in content),
            ('analyzeBtn按钮', 'id="analyzeBtn"' in content),
            ('predictBtn按钮', 'id="predictBtn"' in content),
            ('backtestBtn按钮', 'id="backtestBtn"' in content),
            ('multiIndicatorsBtn按钮', 'id="multiIndicatorsBtn"' in content),
            ('analysisResult容器', 'id="analysisResult"' in content),
            ('analyzeStock函数', 'function analyzeStock()' in content),
            ('predictStock函数', 'function predictStock()' in content),
            ('backtestStock函数', 'function backtestStock()' in content),
            ('analyzeMultiIndicators函数', 'function analyzeMultiIndicators()' in content),
            ('displayAnalysisResult函数', 'function displayAnalysisResult(' in content),
            ('displayMultiIndicatorsResult函数', 'function displayMultiIndicatorsResult(' in content)
        ]

        print("📋 HTML结构检查:")
        all_good = True
        for name, check in checks:
            if check:
                print(f"   ✅ {name}: 存在")
            else:
                print(f"   ❌ {name}: 缺失")
                all_good = False

        return all_good

    except FileNotFoundError:
        print("❌ 找不到 frontend/dark.html 文件")
        return False
    except Exception as e:
        print(f"❌ 检查HTML文件时出错: {e}")
        return False

def check_javascript_syntax():
    """检查JavaScript语法"""
    print("\n🔍 检查JavaScript语法")
    print("="*50)

    try:
        with open('frontend/dark.html', 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取JavaScript代码
        script_start = content.find('<script>')
        script_end = content.find('</script>')

        if script_start == -1 or script_end == -1:
            print("❌ 找不到JavaScript代码块")
            return False

        js_code = content[script_start + 8:script_end]

        # 基本语法检查
        issues = []

        # 检查括号匹配
        open_braces = js_code.count('{')
        close_braces = js_code.count('}')
        if open_braces != close_braces:
            issues.append(f"大括号不匹配: 开{open_braces}, 闭{close_braces}")

        open_parens = js_code.count('(')
        close_parens = js_code.count(')')
        if open_parens != close_parens:
            issues.append(f"小括号不匹配: 开{open_parens}, 闭{close_parens}")

        # 检查常见错误
        if 'function(' in js_code:
            issues.append("可能存在函数定义语法错误")

        if issues:
            print("⚠️  发现潜在问题:")
            for issue in issues:
                print(f"   • {issue}")
            return False
        else:
            print("✅ JavaScript语法检查通过")
            return True

    except Exception as e:
        print(f"❌ 检查JavaScript时出错: {e}")
        return False

def test_cors_and_network():
    """测试CORS和网络问题"""
    print("\n🔍 测试CORS和网络配置")
    print("="*50)

    try:
        # 测试OPTIONS请求
        response = requests.options("http://localhost:8000/api/analyze/000001", timeout=10)
        print(f"✅ OPTIONS请求: {response.status_code}")

        # 检查CORS头
        headers = response.headers
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers'
        ]

        print("📋 CORS头检查:")
        for header in cors_headers:
            if header in headers:
                print(f"   ✅ {header}: {headers[header]}")
            else:
                print(f"   ⚠️  {header}: 缺失")

        return True

    except Exception as e:
        print(f"❌ CORS测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 暗黑主题UI功能检查")
    print("="*60)

    # 测试后端API
    api_results = test_backend_apis()

    # 检查HTML结构
    html_ok = check_html_structure()

    # 检查JavaScript语法
    js_ok = check_javascript_syntax()

    # 测试CORS
    cors_ok = test_cors_and_network()

    # 总结
    print("\n" + "="*60)
    print("📋 功能检查总结")
    print("="*60)

    # API状态
    print("🔌 后端API状态:")
    for name, result in api_results.items():
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"   {status_icon} {name}: {result['status']} ({result['code']})")

    # 前端状态
    print("\n🌐 前端状态:")
    print(f"   {'✅' if html_ok else '❌'} HTML结构: {'正常' if html_ok else '异常'}")
    print(f"   {'✅' if js_ok else '❌'} JavaScript语法: {'正常' if js_ok else '异常'}")
    print(f"   {'✅' if cors_ok else '❌'} CORS配置: {'正常' if cors_ok else '异常'}")

    # 问题诊断
    all_apis_ok = all(r['status'] == 'success' for r in api_results.values())

    if all_apis_ok and html_ok and js_ok and cors_ok:
        print("\n🎉 所有功能检查通过!")
        print("💡 如果仍有问题，请检查:")
        print("   • 浏览器控制台是否有错误信息")
        print("   • 网络请求是否被阻止")
        print("   • 浏览器是否支持现代JavaScript特性")
    else:
        print("\n⚠️  发现问题，建议修复:")
        if not all_apis_ok:
            print("   • 后端API存在问题，检查服务器状态")
        if not html_ok:
            print("   • HTML结构不完整，检查元素ID")
        if not js_ok:
            print("   • JavaScript语法错误，检查代码")
        if not cors_ok:
            print("   • CORS配置问题，检查服务器设置")

    print(f"\n🔗 暗黑主题页面: file:///C:/Users/<USER>/Desktop/qwh/A-AI/frontend/dark.html")

    return 0 if (all_apis_ok and html_ok and js_ok and cors_ok) else 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
