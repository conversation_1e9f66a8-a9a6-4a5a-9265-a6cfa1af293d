<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 A股API测试页面</h1>
    
    <div class="test-section">
        <h2>1. 基础连接测试</h2>
        <button onclick="testConnection()">测试连接</button>
        <div id="connectionResult"></div>
    </div>

    <div class="test-section">
        <h2>2. A股分析测试</h2>
        <input type="text" id="stockCode" placeholder="输入股票代码 (如: 000001)" value="000001">
        <button onclick="testAnalysis()">测试分析</button>
        <div id="analysisResult"></div>
    </div>

    <div class="test-section">
        <h2>3. 技术指标测试</h2>
        <button onclick="testIndicators()">测试技术指标</button>
        <div id="indicatorsResult"></div>
    </div>

    <div class="test-section">
        <h2>4. 错误日志</h2>
        <div id="errorLog"></div>
    </div>

    <script>
        // 错误日志
        function logError(message, error) {
            const errorLog = document.getElementById('errorLog');
            const timestamp = new Date().toLocaleTimeString();
            errorLog.innerHTML += `<div style="margin: 5px 0; padding: 5px; background: #ffe6e6; border-radius: 4px;">
                <strong>[${timestamp}]</strong> ${message}<br>
                <small>${error}</small>
            </div>`;
        }

        // 显示结果
        function showResult(elementId, success, data) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'test-section success';
                element.innerHTML = `<h3>✅ 测试成功</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                element.className = 'test-section error';
                element.innerHTML = `<h3>❌ 测试失败</h3><pre>${data}</pre>`;
            }
        }

        // 测试基础连接
        async function testConnection() {
            try {
                console.log('开始测试连接...');
                const response = await axios.get('http://localhost:8000/');
                console.log('连接测试成功:', response.data);
                showResult('connectionResult', true, response.data);
            } catch (error) {
                console.error('连接测试失败:', error);
                logError('连接测试失败', error.message);
                showResult('connectionResult', false, `错误: ${error.message}`);
            }
        }

        // 测试A股分析
        async function testAnalysis() {
            const stockCode = document.getElementById('stockCode').value || '000001';
            try {
                console.log(`开始测试分析 ${stockCode}...`);
                const response = await axios.get(`http://localhost:8000/api/analyze/${stockCode}`);
                console.log('分析测试成功:', response.data);
                showResult('analysisResult', true, {
                    symbol: response.data.symbol,
                    company_name: response.data.company_name,
                    current_price: response.data.current_price,
                    recommendation: response.data.recommendation,
                    confidence: response.data.confidence,
                    data_source: response.data.data_source || '未知'
                });
            } catch (error) {
                console.error('分析测试失败:', error);
                logError(`分析测试失败 (${stockCode})`, error.message);
                showResult('analysisResult', false, `错误: ${error.message}`);
            }
        }

        // 测试技术指标
        async function testIndicators() {
            try {
                console.log('开始测试技术指标...');
                const response = await axios.get('http://localhost:8000/api/indicators/000001');
                console.log('技术指标测试成功:', response.data);
                showResult('indicatorsResult', true, response.data);
            } catch (error) {
                console.error('技术指标测试失败:', error);
                logError('技术指标测试失败', error.message);
                showResult('indicatorsResult', false, `错误: ${error.message}`);
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testConnection();
        };

        // 全局错误处理
        window.addEventListener('error', function(e) {
            logError('JavaScript错误', e.message);
        });

        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', function(e) {
            logError('Promise错误', e.reason);
        });
    </script>
</body>
</html>
