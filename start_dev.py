#!/usr/bin/env python3
"""
智能量化分析系统开发环境启动脚本
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                🚀 智能量化分析系统                              ║
    ║                专业功能 · 傻瓜操作                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")

    # 检查Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print("❌ Python版本过低，需要Python 3.8+")
            return False
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except Exception as e:
        print(f"❌ Python检查失败: {e}")
        return False

    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ Node.js {result.stdout.strip()}")
        else:
            print("❌ Node.js未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False

    # 检查npm
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ npm {result.stdout.strip()}")
        else:
            print("❌ npm未安装")
            return False
    except FileNotFoundError:
        print("❌ npm未安装")
        return False

    return True

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")

    # 创建必要的目录
    directories = ['logs', 'data', 'models']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 创建目录: {directory}")

    # 复制环境配置文件
    if not Path('.env').exists():
        if Path('.env.example').exists():
            import shutil
            shutil.copy('.env.example', '.env')
            print("📝 创建 .env 文件")
        else:
            # 创建基本的.env文件
            with open('.env', 'w', encoding='utf-8') as f:
                f.write("""# 智能量化分析系统配置
APP_NAME=智能量化分析系统
DEBUG=True
SECRET_KEY=dev-secret-key-change-in-production
DATABASE_URL=sqlite:///./quant_analysis.db
REDIS_URL=redis://localhost:6379/0
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
""")
            print("📝 创建基本 .env 文件")

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")

    # 安装Python依赖
    print("🐍 安装Python依赖...")
    try:
        # 只安装核心依赖，避免复杂的机器学习库
        core_deps = [
            'fastapi==0.104.1',
            'uvicorn[standard]==0.24.0',
            'pydantic==2.5.0',
            'pydantic-settings==2.1.0',
            'pandas==2.1.3',
            'numpy==1.25.2',
            'yfinance==0.2.28',
            'ta==0.10.2',
            'scikit-learn==1.3.2',
            'requests==2.31.0',
            'python-dotenv==1.0.0'
        ]

        for dep in core_deps:
            print(f"  安装 {dep}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep],
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"  ⚠️ {dep} 安装失败，跳过")
            else:
                print(f"  ✅ {dep} 安装成功")

    except Exception as e:
        print(f"❌ Python依赖安装失败: {e}")
        return False

    # 安装前端依赖
    print("🎨 安装前端依赖...")
    try:
        os.chdir('frontend')
        result = subprocess.run(['npm', 'install'], capture_output=True, text=True)
        os.chdir('..')
        if result.returncode == 0:
            print("✅ 前端依赖安装成功")
        else:
            print(f"❌ 前端依赖安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 前端依赖安装失败: {e}")
        return False

    return True

def start_backend():
    """启动后端服务"""
    print("🔧 启动后端服务...")
    try:
        os.chdir('backend')
        process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn',
            'app.main:app',
            '--reload',
            '--host', '0.0.0.0',
            '--port', '8000'
        ])
        os.chdir('..')
        return process
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🎨 启动前端服务...")
    try:
        os.chdir('frontend')
        process = subprocess.Popen(['npm', 'run', 'dev'])
        os.chdir('..')
        return process
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None

def main():
    """主函数"""
    print_banner()

    # 检查系统要求
    if not check_requirements():
        print("❌ 系统要求检查失败")
        return 1

    # 设置环境
    setup_environment()

    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        return 1

    # 启动服务
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端启动失败")
        return 1

    # 等待后端启动
    print("⏳ 等待后端启动...")
    time.sleep(5)

    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ 前端启动失败")
        backend_process.terminate()
        return 1

    # 显示访问信息
    print("\n" + "="*60)
    print("✅ 系统启动完成！")
    print("")
    print("📱 前端地址: http://localhost:3000")
    print("🔧 后端地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("")
    print("按 Ctrl+C 停止服务")
    print("="*60)

    # 等待用户中断
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")

        # 停止进程
        if backend_process:
            backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()

        # 等待进程结束
        time.sleep(2)

        print("✅ 服务已停止")
        return 0

if __name__ == '__main__':
    sys.exit(main())
