#!/usr/bin/env python3
"""
测试暗黑主题UI界面
"""

import time
import webbrowser
import os
from pathlib import Path

def test_dark_ui():
    """测试暗黑主题UI"""
    print("🌙 测试暗黑主题UI界面")
    print("="*50)
    
    # 获取文件路径
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    
    if not dark_html_path.exists():
        print(f"❌ 文件不存在: {dark_html_path}")
        return False
    
    print(f"✅ 找到暗黑主题文件: {dark_html_path}")
    
    # 转换为file:// URL
    file_url = f"file:///{dark_html_path.absolute().as_posix()}"
    print(f"🌐 文件URL: {file_url}")
    
    # 打开浏览器
    try:
        print("🚀 正在打开暗黑主题界面...")
        webbrowser.open(file_url)
        print("✅ 浏览器已打开")
        
        print("\n🎨 暗黑主题UI特色:")
        print("   • 🌙 深色背景 (#0a0a0a)")
        print("   • ✨ 渐变按钮和悬停效果")
        print("   • 🎯 专业级配色方案")
        print("   • 📱 响应式设计")
        print("   • 🔥 现代化图标和动画")
        print("   • 💎 玻璃拟态效果")
        print("   • 🎪 彩色信号标签")
        print("   • 📊 精美的数据卡片")
        
        print("\n🧪 测试建议:")
        print("   1. 输入股票代码 (如: 000001)")
        print("   2. 点击不同的分析按钮")
        print("   3. 观察加载动画效果")
        print("   4. 查看分析结果的暗黑主题展示")
        print("   5. 测试响应式布局 (调整浏览器窗口大小)")
        print("   6. 体验悬停效果和动画")
        
        print("\n⏳ 等待用户测试...")
        input("按回车键继续...")
        
        return True
        
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        return False

def compare_themes():
    """对比两个主题"""
    print("\n🔄 主题对比")
    print("="*50)
    
    print("📊 原版主题 vs 暗黑主题:")
    print()
    
    print("🎨 视觉设计:")
    print("   原版: 白色背景 + 蓝紫渐变")
    print("   暗黑: 深色背景 + 多彩渐变")
    print()
    
    print("🎯 用户体验:")
    print("   原版: 清新明亮，适合日间使用")
    print("   暗黑: 护眼舒适，适合长时间使用")
    print()
    
    print("💎 设计风格:")
    print("   原版: 简洁友好，面向大众")
    print("   暗黑: 专业高级，面向专业用户")
    print()
    
    print("🔧 技术特色:")
    print("   原版: 基础动画，标准布局")
    print("   暗黑: 高级动画，现代布局")
    print()
    
    print("🎪 色彩系统:")
    print("   原版: 蓝色主调")
    print("   暗黑: 多色彩系统 (蓝/紫/绿/红/橙/黄)")

def main():
    """主函数"""
    print("🎨 A股量化分析系统 - 暗黑主题UI测试")
    print("="*60)
    
    # 测试暗黑主题
    success = test_dark_ui()
    
    if success:
        # 主题对比
        compare_themes()
        
        print("\n" + "="*60)
        print("🎉 暗黑主题UI测试完成!")
        print("="*60)
        
        print("✅ 暗黑主题特色:")
        print("   🌙 深色护眼设计")
        print("   ✨ 现代化视觉效果")
        print("   🎯 专业级用户体验")
        print("   📱 完美响应式布局")
        print("   🔥 流畅动画交互")
        print("   💎 高级配色方案")
        
        print("\n💡 使用建议:")
        print("   • 适合专业投资者和量化分析师")
        print("   • 长时间使用更护眼舒适")
        print("   • 在暗光环境下体验更佳")
        print("   • 支持所有原有功能")
        
        print("\n🔗 访问地址:")
        current_dir = Path(__file__).parent
        dark_html_path = current_dir / "frontend" / "dark.html"
        file_url = f"file:///{dark_html_path.absolute().as_posix()}"
        print(f"   {file_url}")
        
        return 0
    else:
        print("\n❌ 暗黑主题UI测试失败")
        return 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
