#!/usr/bin/env python3
"""
测试收藏功能
"""

import webbrowser
from pathlib import Path

def test_favorites_feature():
    """测试收藏功能"""
    print("🔍 测试收藏功能")
    print("="*50)
    
    # 检查HTML文件是否存在
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    
    if not dark_html_path.exists():
        print(f"❌ 文件不存在: {dark_html_path}")
        return False
    
    # 检查收藏功能相关代码
    try:
        with open(dark_html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查收藏功能的关键元素
        features = [
            ('收藏按钮', 'id="favoriteBtn"' in content),
            ('管理收藏按钮', 'id="manageFavoritesBtn"' in content),
            ('收藏面板', 'id="favoritesPanel"' in content),
            ('收藏列表', 'id="favoritesList"' in content),
            ('收藏功能JavaScript', 'toggleFavorite()' in content),
            ('快速分析函数', 'quickAnalyze(' in content),
            ('本地存储', 'localStorage.getItem' in content),
            ('收藏样式', 'btn-favorite' in content),
            ('通知功能', 'showNotification(' in content),
            ('右键删除', 'oncontextmenu=' in content)
        ]
        
        print("📋 收藏功能检查:")
        all_features = True
        for name, check in features:
            if check:
                print(f"   ✅ {name}: 已实现")
            else:
                print(f"   ❌ {name}: 未实现")
                all_features = False
        
        return all_features
        
    except Exception as e:
        print(f"❌ 检查收藏功能失败: {e}")
        return False

def open_test_page():
    """打开测试页面"""
    print("\n🚀 打开收藏功能测试页面")
    print("="*50)
    
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    
    if dark_html_path.exists():
        file_url = f"file:///{dark_html_path.absolute().as_posix()}"
        print(f"🌐 打开URL: {file_url}")
        
        try:
            webbrowser.open(file_url)
            print("✅ 浏览器已打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return False
    else:
        print(f"❌ 文件不存在: {dark_html_path}")
        return False

def print_usage_guide():
    """打印使用指南"""
    print("\n📖 收藏功能使用指南")
    print("="*50)
    
    print("🎯 基本操作:")
    print("   1. 输入股票代码 (如: 000001)")
    print("   2. 点击 '收藏股票' 按钮添加到收藏")
    print("   3. 点击 '管理收藏' 查看收藏列表")
    print("   4. 左键点击收藏的股票进行快速分析")
    print("   5. 右键点击收藏的股票删除收藏")
    
    print("\n✨ 功能特色:")
    print("   • 🔄 自动保存: 收藏数据保存在浏览器本地存储")
    print("   • 💖 状态显示: 收藏按钮会显示当前收藏状态")
    print("   • 🚀 快速分析: 一键分析收藏的股票")
    print("   • 🗑️ 右键删除: 右键菜单删除收藏")
    print("   • 📱 响应式: 完美适配各种屏幕尺寸")
    print("   • 🎨 暗黑主题: 护眼的暗色界面")
    print("   • 🔔 通知提示: 操作成功/失败的友好提示")
    
    print("\n🧪 测试建议:")
    print("   1. 测试添加收藏功能")
    print("   2. 测试收藏状态显示")
    print("   3. 测试收藏列表显示")
    print("   4. 测试快速分析功能")
    print("   5. 测试右键删除功能")
    print("   6. 测试数据持久化 (刷新页面后收藏是否保留)")
    print("   7. 测试通知提示效果")
    
    print("\n💡 技术特点:")
    print("   • 使用localStorage实现数据持久化")
    print("   • 响应式设计适配移动端")
    print("   • 优雅的动画和过渡效果")
    print("   • 完善的错误处理和用户反馈")

def main():
    """主函数"""
    print("🔧 收藏功能测试")
    print("="*60)
    
    # 测试收藏功能实现
    features_ok = test_favorites_feature()
    
    # 打开测试页面
    browser_opened = open_test_page()
    
    # 打印使用指南
    print_usage_guide()
    
    # 总结
    print("\n" + "="*60)
    print("📋 收藏功能测试总结")
    print("="*60)
    
    print("✅ 功能实现状态:")
    print(f"   {'✅' if features_ok else '❌'} 收藏功能代码: {'完整' if features_ok else '不完整'}")
    print(f"   {'✅' if browser_opened else '❌'} 浏览器打开: {'成功' if browser_opened else '失败'}")
    
    print("\n🎯 主要改进:")
    print("   • 移除了固定的热门股票列表")
    print("   • 添加了个性化的收藏功能")
    print("   • 支持用户自定义收藏股票")
    print("   • 提供快速分析收藏股票的功能")
    print("   • 实现了数据持久化存储")
    
    print("\n🌟 用户体验提升:")
    print("   • 个性化: 用户可以收藏自己关注的股票")
    print("   • 便捷性: 一键快速分析收藏的股票")
    print("   • 持久性: 收藏数据永久保存")
    print("   • 管理性: 方便的收藏管理功能")
    print("   • 视觉性: 美观的收藏状态显示")
    
    print("\n🔗 测试地址:")
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    file_url = f"file:///{dark_html_path.absolute().as_posix()}"
    print(f"   {file_url}")
    
    if features_ok and browser_opened:
        print("\n🎉 收藏功能实现完成！")
        print("   现在用户可以收藏自己关注的股票并快速分析！")
    else:
        print("\n⚠️ 收藏功能实现存在问题，请检查代码。")
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
