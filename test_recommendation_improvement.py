#!/usr/bin/env python3
"""
测试推荐操作改进效果
"""

import requests
import webbrowser
from pathlib import Path

def test_recommendation_display():
    """测试推荐操作显示改进"""
    print("🔍 测试推荐操作显示改进")
    print("="*50)
    
    base_url = "http://localhost:8000"
    test_symbols = ["000001", "600519"]
    
    for symbol in test_symbols:
        print(f"\n📊 测试股票: {symbol}")
        print("-" * 30)
        
        try:
            response = requests.get(f"{base_url}/api/analyze/{symbol}", timeout=30)
            if response.status_code == 200:
                data = response.json()
                print("✅ AI分析API正常")
                
                # 获取推荐操作
                recommendation = data.get('recommendation', 'N/A')
                confidence = data.get('confidence', 0)
                target_price = data.get('target_price', 0)
                expected_return = data.get('expected_return', 0)
                time_horizon = data.get('time_horizon', 'N/A')
                
                print(f"📈 原始推荐操作: {recommendation}")
                print(f"🎯 AI信心指数: {confidence*100:.1f}%")
                print(f"💰 目标价位: ¥{target_price:.2f}")
                print(f"📊 预期收益: {expected_return*100:.1f}%")
                print(f"⏰ 建议持有期: {time_horizon}")
                
                # 模拟前端转换
                print(f"\n🎨 小白用户友好显示:")
                user_friendly = convert_recommendation_to_user_friendly(recommendation)
                print(f"   图标: {user_friendly['icon']}")
                print(f"   操作: {user_friendly['text']}")
                print(f"   说明: {user_friendly['description']}")
                print(f"   样式: {user_friendly['style']}")
                print(f"   持有期: {convert_time_horizon(time_horizon)}")
                
            else:
                print(f"❌ AI分析API错误: {response.status_code}")
        except Exception as e:
            print(f"❌ AI分析API异常: {e}")

def convert_recommendation_to_user_friendly(recommendation):
    """转换推荐操作为用户友好格式"""
    recommendations = {
        'BUY': {
            'icon': '🚀',
            'text': '建议买入',
            'description': '根据技术分析，该股票具有上涨潜力，适合买入建仓',
            'style': 'bg-green-600/20 border border-green-500/30'
        },
        'STRONG_BUY': {
            'icon': '💎',
            'text': '强烈建议买入',
            'description': '多项指标显示强烈买入信号，建议积极配置',
            'style': 'bg-emerald-600/20 border border-emerald-500/30'
        },
        'HOLD': {
            'icon': '🤝',
            'text': '建议持有',
            'description': '当前价位相对合理，建议继续持有，等待更好时机',
            'style': 'bg-blue-600/20 border border-blue-500/30'
        },
        'SELL': {
            'icon': '📉',
            'text': '建议卖出',
            'description': '技术指标显示下跌风险，建议适当减仓或卖出',
            'style': 'bg-orange-600/20 border border-orange-500/30'
        },
        'STRONG_SELL': {
            'icon': '⚠️',
            'text': '强烈建议卖出',
            'description': '多项指标显示强烈卖出信号，建议尽快减仓',
            'style': 'bg-red-600/20 border border-red-500/30'
        }
    }
    
    return recommendations.get(recommendation, {
        'icon': '❓',
        'text': '暂无建议',
        'description': '请结合市场情况和个人风险承受能力做出投资决策',
        'style': 'bg-gray-600/20 border border-gray-500/30'
    })

def convert_time_horizon(time_horizon):
    """转换投资周期为用户友好格式"""
    if not time_horizon or time_horizon == 'N/A':
        return '根据市场情况'
    
    # 如果是数字，转换为天数描述
    if isinstance(time_horizon, (int, float)):
        if time_horizon <= 7:
            return f"{time_horizon}天 (短期)"
        elif time_horizon <= 30:
            return f"{time_horizon}天 (中短期)"
        elif time_horizon <= 90:
            return f"{time_horizon}天 (中期)"
        else:
            return f"{time_horizon}天 (长期)"
    
    # 如果是字符串，直接返回
    return str(time_horizon)

def test_ui_improvements():
    """测试UI改进"""
    print("\n🔧 测试UI改进效果")
    print("="*50)
    
    try:
        with open('frontend/dark.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查改进项目
        improvements = [
            ('推荐操作转换函数', 'getRecommendationText(' in content),
            ('推荐图标函数', 'getRecommendationIcon(' in content),
            ('推荐描述函数', 'getRecommendationDescription(' in content),
            ('推荐样式函数', 'getRecommendationStyle(' in content),
            ('时间周期转换', 'getTimeHorizonText(' in content),
            ('用户友好显示', 'AI信心指数' in content),
            ('目标价位显示', '目标价位' in content),
            ('建议持有期显示', '建议持有期' in content),
            ('图标和文字显示', 'getRecommendationIcon(data.recommendation)' in content),
            ('样式应用', 'getRecommendationStyle(data.recommendation)' in content)
        ]
        
        print("📋 UI改进检查:")
        all_improved = True
        for name, check in improvements:
            if check:
                print(f"   ✅ {name}: 已实现")
            else:
                print(f"   ❌ {name}: 未实现")
                all_improved = False
        
        return all_improved
        
    except Exception as e:
        print(f"❌ 检查UI改进失败: {e}")
        return False

def open_test_page():
    """打开测试页面"""
    print("\n🚀 打开改进后的测试页面")
    print("="*50)
    
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    
    if dark_html_path.exists():
        file_url = f"file:///{dark_html_path.absolute().as_posix()}"
        print(f"🌐 打开URL: {file_url}")
        
        try:
            webbrowser.open(file_url)
            print("✅ 浏览器已打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return False
    else:
        print(f"❌ 文件不存在: {dark_html_path}")
        return False

def print_improvement_guide():
    """打印改进指南"""
    print("\n📖 推荐操作改进指南")
    print("="*50)
    
    print("🎯 改进前后对比:")
    print("   ❌ 改进前: HOLD (专业术语，小白用户不理解)")
    print("   ✅ 改进后: 🤝 建议持有 (图标+中文，直观易懂)")
    
    print("\n🎨 新的显示格式:")
    print("   📊 推荐操作转换:")
    print("      • BUY → 🚀 建议买入")
    print("      • STRONG_BUY → 💎 强烈建议买入")
    print("      • HOLD → 🤝 建议持有")
    print("      • SELL → 📉 建议卖出")
    print("      • STRONG_SELL → ⚠️ 强烈建议卖出")
    
    print("\n   📝 术语优化:")
    print("      • 置信度 → AI信心指数")
    print("      • 目标价格 → 目标价位")
    print("      • 投资周期 → 建议持有期")
    
    print("\n   🎪 视觉增强:")
    print("      • 添加表情图标增加亲和力")
    print("      • 彩色背景区分不同建议")
    print("      • 详细说明帮助理解")
    print("      • 网格布局更加整洁")
    
    print("\n🧪 测试建议:")
    print("   1. 输入股票代码 (如: 000001)")
    print("   2. 点击 'AI分析' 按钮")
    print("   3. 查看投资建议部分的显示效果")
    print("   4. 验证是否显示中文和图标")
    print("   5. 检查详细说明是否易懂")

def main():
    """主函数"""
    print("🔧 推荐操作显示改进测试")
    print("="*60)
    
    # 测试推荐操作显示
    test_recommendation_display()
    
    # 测试UI改进
    ui_improved = test_ui_improvements()
    
    # 打开测试页面
    browser_opened = open_test_page()
    
    # 打印改进指南
    print_improvement_guide()
    
    # 总结
    print("\n" + "="*60)
    print("📋 推荐操作改进总结")
    print("="*60)
    
    print("✅ 改进内容:")
    print("   • 专业术语转换为中文表述")
    print("   • 添加表情图标增加亲和力")
    print("   • 提供详细的操作说明")
    print("   • 优化字段名称更易理解")
    print("   • 彩色背景区分不同建议")
    print("   • 网格布局提升视觉效果")
    
    print(f"\n🔧 改进状态:")
    print(f"   {'✅' if ui_improved else '❌'} UI代码改进: {'完成' if ui_improved else '未完成'}")
    print(f"   {'✅' if browser_opened else '❌'} 浏览器打开: {'成功' if browser_opened else '失败'}")
    
    print("\n🎯 用户体验提升:")
    print("   • 小白用户友好: 不再显示英文专业术语")
    print("   • 视觉直观: 图标和颜色帮助快速理解")
    print("   • 详细说明: 每个建议都有具体解释")
    print("   • 术语优化: 使用更贴近用户的表述")
    
    print("\n💡 显示效果:")
    print("   🚀 建议买入: 绿色背景，上涨潜力说明")
    print("   💎 强烈建议买入: 翠绿背景，强烈信号说明")
    print("   🤝 建议持有: 蓝色背景，合理价位说明")
    print("   📉 建议卖出: 橙色背景，下跌风险说明")
    print("   ⚠️ 强烈建议卖出: 红色背景，强烈信号说明")
    
    print("\n🔗 测试地址:")
    current_dir = Path(__file__).parent
    dark_html_path = current_dir / "frontend" / "dark.html"
    file_url = f"file:///{dark_html_path.absolute().as_posix()}"
    print(f"   {file_url}")
    
    if ui_improved and browser_opened:
        print("\n🎉 推荐操作显示改进完成！")
        print("   现在小白用户也能轻松理解AI的投资建议！")
    else:
        print("\n⚠️ 推荐操作改进存在问题，请检查代码。")
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
