#!/usr/bin/env python3
"""
快速API测试
"""

import requests
import json

def test_api():
    """测试API"""
    try:
        print("🔍 测试AI分析API...")
        response = requests.get("http://localhost:8000/api/analyze/000001", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            print(f"📈 推荐操作: {data.get('recommendation', 'N/A')}")
            print(f"🎯 置信度: {data.get('confidence', 0)*100:.1f}%")
            
            summary = data.get('summary', '')
            print(f"📝 分析摘要: {summary}")
            
            # 检查摘要长度和内容来判断是否使用了大模型
            if len(summary) > 200 and ('技术面' in summary or '分析' in summary):
                print("🤖 可能使用了DeepSeek大模型")
            else:
                print("🔧 使用规则引擎")
                
            print(f"\n完整响应:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
        else:
            print(f"❌ API错误: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    test_api()
