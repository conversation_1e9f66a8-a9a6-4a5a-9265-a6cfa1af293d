#!/usr/bin/env python3
"""
测试正在运行的A股智能量化分析系统
"""

import requests
import json
import time

def test_api_endpoints():
    """测试API端点"""
    print("🧪 测试API端点")
    print("="*50)
    
    base_url = "http://localhost:8000"
    
    # 测试端点列表
    endpoints = [
        ("GET", "/", "系统状态"),
        ("GET", "/health", "健康检查"),
        ("GET", "/docs", "API文档"),
        ("GET", "/api/analyze/000001", "A股分析"),
        ("GET", "/api/predict/000001", "走势预测"),
        ("GET", "/api/backtest/000001/ma_crossover", "策略回测"),
    ]
    
    results = []
    
    for method, endpoint, description in endpoints:
        print(f"\n🔍 测试 {description}: {method} {endpoint}")
        
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=30)
            
            if response.status_code == 200:
                print(f"     ✅ 成功 (200)")
                if endpoint.startswith("/api/"):
                    # 显示部分响应数据
                    try:
                        data = response.json()
                        if isinstance(data, dict):
                            keys = list(data.keys())[:3]
                            print(f"     📊 响应字段: {keys}")
                    except:
                        print(f"     📄 响应长度: {len(response.text)} 字符")
                results.append((endpoint, "✅", "成功"))
            else:
                print(f"     ❌ 失败 ({response.status_code})")
                results.append((endpoint, "❌", f"HTTP {response.status_code}"))
                
        except requests.exceptions.Timeout:
            print(f"     ⏰ 超时")
            results.append((endpoint, "⏰", "超时"))
        except requests.exceptions.ConnectionError:
            print(f"     🔌 连接失败")
            results.append((endpoint, "🔌", "连接失败"))
        except Exception as e:
            print(f"     ❌ 异常: {e}")
            results.append((endpoint, "❌", str(e)))
    
    return results

def test_a_stock_analysis():
    """测试A股分析功能"""
    print("\n🤖 测试A股分析功能")
    print("="*50)
    
    test_stocks = ["000001", "600519", "000858"]
    
    for stock in test_stocks:
        print(f"\n📈 分析 {stock}:")
        
        try:
            response = requests.get(f"http://localhost:8000/api/analyze/{stock}", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                print(f"     ✅ 分析成功")
                print(f"     📊 股票: {data.get('symbol', 'N/A')}")
                print(f"     💰 当前价格: ¥{data.get('current_price', 'N/A')}")
                print(f"     🎯 建议: {data.get('recommendation', 'N/A')}")
                print(f"     🔮 置信度: {data.get('confidence', 'N/A')}")
            else:
                print(f"     ❌ 分析失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"     ❌ 异常: {e}")

def test_prediction():
    """测试走势预测功能"""
    print("\n🔮 测试走势预测功能")
    print("="*50)
    
    try:
        response = requests.get("http://localhost:8000/api/predict/000001", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 预测成功")
            print(f"📈 股票: {data.get('symbol', 'N/A')}")
            print(f"💰 当前价格: ¥{data.get('current_price', 'N/A')}")
            print(f"📊 预测周期: {data.get('prediction_horizon', 'N/A')}天")
            print(f"🎯 趋势方向: {data.get('trend_direction', 'N/A')}")
            
            predictions = data.get('predictions', [])[:3]
            print(f"📈 未来3天预测:")
            for i, pred in enumerate(predictions):
                days = i + 1
                price = pred.get('predicted_price', 'N/A')
                confidence = pred.get('confidence', 0) * 100
                print(f"   {days}天后: ¥{price} (置信度:{confidence:.0f}%)")
        else:
            print(f"❌ 预测失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 预测异常: {e}")

def show_system_status():
    """显示系统状态"""
    print("\n📊 系统运行状态")
    print("="*60)
    print("🟢 后端服务: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🌐 前端页面: file:///C:/Users/<USER>/Desktop/qwh/A-AI/frontend/simple.html")
    print("")
    print("🎯 主要功能:")
    print("   🤖 AI智能分析 - 一键获取买卖建议")
    print("   🔮 走势预测 - 机器学习预测股价")
    print("   📊 策略回测 - 验证交易策略效果")
    print("")
    print("💡 使用方法:")
    print("   1. 打开前端页面")
    print("   2. 输入A股代码 (如: 000001, 600519)")
    print("   3. 点击相应功能按钮")
    print("   4. 查看分析结果")

def main():
    """主函数"""
    print("🚀 A股智能量化分析系统 - 运行测试")
    print("="*60)
    print("⏳ 开始测试...")
    
    # 测试API端点
    results = test_api_endpoints()
    
    # 测试A股分析
    test_a_stock_analysis()
    
    # 测试走势预测
    test_prediction()
    
    # 显示测试结果汇总
    print("\n" + "="*60)
    print("📋 测试结果汇总")
    print("="*60)
    
    for endpoint, status, message in results:
        print(f"{status} {endpoint:<30} - {message}")
    
    # 显示系统状态
    show_system_status()
    
    print("\n🎉 系统测试完成！")
    print("💡 如果所有功能正常，您现在可以开始使用系统了！")
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
