try:
    import tushare as ts
    TUSHARE_AVAILABLE = True
except ImportError:
    TUSHARE_AVAILABLE = False

import pandas as pd
import numpy as np
try:
    import ta
    TA_AVAILABLE = True
except ImportError:
    TA_AVAILABLE = False

from typing import Dict, List, Optional, Any
import logging
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)

class AStockAnalyzer:
    """A股数据分析器"""

    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        # 初始化tushare
        if TUSHARE_AVAILABLE:
            try:
                # 这里需要设置tushare token
                ts.set_token('cdff77dece15e2ada5370997aad8f493d9a3e15b751e1672ec67771e')  # 需要用户自己申请token
                self.pro = ts.pro_api()
                logger.info("Tushare初始化成功")
            except Exception as e:
                logger.warning(f"Tushare初始化失败: {e}, 将使用模拟数据")
                self.pro = None
        else:
            logger.warning("Tushare未安装，将使用模拟数据")
            self.pro = None

    def _normalize_stock_code(self, symbol: str) -> str:
        """标准化股票代码"""
        # 移除空格和特殊字符
        symbol = re.sub(r'[^\w]', '', symbol.upper())

        # 如果是6位数字，自动添加后缀
        if symbol.isdigit() and len(symbol) == 6:
            if symbol.startswith('6'):
                return f"{symbol}.SH"  # 上交所
            elif symbol.startswith(('0', '3')):
                return f"{symbol}.SZ"  # 深交所

        # 如果已经有后缀，直接返回
        if '.' in symbol:
            return symbol

        # 默认处理
        return symbol

    async def get_stock_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取A股数据"""
        try:
            # 标准化股票代码
            ts_code = self._normalize_stock_code(symbol)

            # 检查缓存
            cache_key = f"a_stock_data:{ts_code}"
            if cache_key in self.cache:
                cached_time, data = self.cache[cache_key]
                if (datetime.now() - cached_time).seconds < self.cache_timeout:
                    return data

            if self.pro is None:
                # 使用免费API获取数据
                return await self._get_free_api_data(ts_code)

            # 获取股票基本信息
            try:
                stock_basic = self.pro.stock_basic(ts_code=ts_code, fields='ts_code,symbol,name,area,industry,market,list_date')
                if stock_basic.empty:
                    logger.warning(f"Pro API未找到股票信息: {ts_code}，尝试免费API")
                    return await self._get_free_api_data(ts_code)

                stock_info = stock_basic.iloc[0]

                # 获取历史数据 (最近1年)
                end_date = datetime.now().strftime('%Y%m%d')
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')

                hist_data = self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
                if hist_data.empty:
                    logger.warning(f"Pro API未找到历史数据: {ts_code}，尝试免费API")
                    return await self._get_free_api_data(ts_code)
            except Exception as e:
                logger.warning(f"Pro API调用失败: {e}，尝试免费API")
                return await self._get_free_api_data(ts_code)

            # 按日期排序
            hist_data = hist_data.sort_values('trade_date')
            hist_data.reset_index(drop=True, inplace=True)

            # 获取最新价格
            latest_data = hist_data.iloc[-1]
            current_price = latest_data['close']

            # 计算价格变化
            if len(hist_data) > 1:
                prev_price = hist_data.iloc[-2]['close']
                price_change = current_price - prev_price
                price_change_percent = (price_change / prev_price) * 100
            else:
                price_change = 0
                price_change_percent = 0

            # 准备价格历史数据
            price_history = []
            recent_data = hist_data.tail(60)  # 最近60天
            for _, row in recent_data.iterrows():
                price_history.append({
                    'date': pd.to_datetime(row['trade_date']).strftime('%Y-%m-%d'),
                    'price': float(row['close']),
                    'volume': int(row['vol'] * 100)  # 转换为股数
                })

            # 构建结果
            result = {
                'symbol': symbol,
                'ts_code': ts_code,
                'company_name': stock_info['name'],
                'current_price': float(current_price),
                'price_change': float(price_change),
                'price_change_percent': float(price_change_percent),
                'volume': int(latest_data['vol'] * 100),
                'market': stock_info['market'],
                'industry': stock_info['industry'],
                'area': stock_info['area'],
                'price_history': price_history,
                'raw_data': hist_data,
                'timestamp': datetime.now().isoformat()
            }

            # 缓存结果
            self.cache[cache_key] = (datetime.now(), result)

            logger.info(f"获取A股数据成功: {ts_code}")
            return result

        except Exception as e:
            logger.error(f"获取A股数据失败 {symbol}: {str(e)}")
            return self._get_mock_data(symbol)

    async def _get_free_api_data(self, symbol: str) -> Dict[str, Any]:
        """使用tushare免费API获取数据"""
        try:
            if not TUSHARE_AVAILABLE:
                logger.warning("Tushare不可用，使用模拟数据")
                return self._get_mock_data(symbol)

            import tushare as ts

            # 提取股票代码 (去掉交易所后缀)
            stock_code = symbol.split('.')[0]

            # 获取实时数据
            realtime_data = ts.get_realtime_quotes(stock_code)
            if realtime_data is None or realtime_data.empty:
                logger.warning(f"免费API未获取到实时数据: {stock_code}")
                return self._get_mock_data(symbol)

            stock_info = realtime_data.iloc[0]

            # 获取历史数据 (最近60天)
            try:
                hist_data = ts.get_hist_data(stock_code, start='2024-01-01')
                if hist_data is None or hist_data.empty:
                    logger.warning(f"免费API未获取到历史数据: {stock_code}")
                    # 使用实时数据生成简单历史数据
                    hist_data = self._generate_simple_history(float(stock_info['price']))
            except Exception as e:
                logger.warning(f"获取历史数据失败: {e}")
                hist_data = self._generate_simple_history(float(stock_info['price']))

            # 解析实时数据
            current_price = float(stock_info['price'])
            prev_close = float(stock_info['pre_close'])
            price_change = current_price - prev_close
            price_change_percent = (price_change / prev_close) * 100 if prev_close > 0 else 0

            # 准备价格历史数据
            price_history = []
            if isinstance(hist_data, pd.DataFrame) and not hist_data.empty:
                # 按日期排序
                hist_data = hist_data.sort_index()
                for date, row in hist_data.tail(60).iterrows():
                    price_history.append({
                        'date': date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date),
                        'price': float(row['close']) if 'close' in row else float(row.get('price', current_price)),
                        'volume': int(row.get('volume', 0)) if 'volume' in row else 0
                    })

            # 如果没有历史数据，生成简单数据
            if not price_history:
                price_history = self._generate_simple_price_history(current_price)

            # 构建DataFrame用于技术指标计算
            if isinstance(hist_data, pd.DataFrame) and not hist_data.empty:
                raw_data = hist_data.copy()
                # 确保列名正确
                if 'close' not in raw_data.columns and 'price' in raw_data.columns:
                    raw_data['close'] = raw_data['price']
            else:
                # 创建简单的DataFrame
                raw_data = pd.DataFrame({
                    'close': [current_price] * 30,
                    'open': [current_price * 0.99] * 30,
                    'high': [current_price * 1.01] * 30,
                    'low': [current_price * 0.99] * 30,
                    'vol': [1000000] * 30
                })

            result = {
                'symbol': symbol,
                'ts_code': symbol,
                'company_name': stock_info.get('name', f"股票{stock_code}"),
                'current_price': current_price,
                'price_change': price_change,
                'price_change_percent': price_change_percent,
                'volume': int(float(stock_info.get('volume', 0))),
                'market': '主板',
                'industry': '未知',
                'area': '中国',
                'price_history': price_history,
                'raw_data': raw_data,
                'timestamp': datetime.now().isoformat(),
                'data_source': 'tushare_free_api'
            }

            logger.info(f"使用免费API获取A股数据成功: {symbol}")
            return result

        except Exception as e:
            logger.error(f"免费API获取数据失败 {symbol}: {str(e)}")
            return self._get_mock_data(symbol)

    def _generate_simple_history(self, base_price: float) -> pd.DataFrame:
        """生成简单的历史数据"""
        import random
        dates = pd.date_range(end=datetime.now(), periods=60, freq='D')
        data = []

        for i, date in enumerate(dates):
            price = base_price * (1 + random.uniform(-0.05, 0.05))
            data.append({
                'close': price,
                'open': price * 0.99,
                'high': price * 1.02,
                'low': price * 0.98,
                'volume': random.randint(1000000, 10000000)
            })

        return pd.DataFrame(data, index=dates)

    def _generate_simple_price_history(self, current_price: float) -> List[Dict]:
        """生成简单的价格历史"""
        import random
        history = []

        for i in range(60):
            date = (datetime.now() - timedelta(days=59-i)).strftime('%Y-%m-%d')
            price = current_price * (1 + random.uniform(-0.1, 0.1))
            volume = random.randint(1000000, 50000000)
            history.append({
                'date': date,
                'price': price,
                'volume': volume
            })

        return history

    def _get_mock_data(self, symbol: str) -> Dict[str, Any]:
        """生成模拟A股数据"""
        import random

        # 模拟股票信息
        mock_names = {
            '000001.SZ': '平安银行',
            '000002.SZ': '万科A',
            '600000.SH': '浦发银行',
            '600036.SH': '招商银行',
            '600519.SH': '贵州茅台',
            '000858.SZ': '五粮液',
            '002415.SZ': '海康威视',
            '300059.SZ': '东方财富'
        }

        ts_code = self._normalize_stock_code(symbol)
        company_name = mock_names.get(ts_code, f"模拟股票{symbol}")

        # 生成模拟价格数据
        base_price = random.uniform(10, 200)
        current_price = base_price * random.uniform(0.95, 1.05)
        prev_price = base_price * random.uniform(0.95, 1.05)
        price_change = current_price - prev_price
        price_change_percent = (price_change / prev_price) * 100

        # 生成历史数据
        price_history = []
        for i in range(60):
            date = (datetime.now() - timedelta(days=59-i)).strftime('%Y-%m-%d')
            price = base_price * random.uniform(0.9, 1.1)
            volume = random.randint(1000000, 50000000)
            price_history.append({
                'date': date,
                'price': price,
                'volume': volume
            })

        # 创建模拟DataFrame
        mock_df = pd.DataFrame({
            'trade_date': [item['date'].replace('-', '') for item in price_history],
            'close': [item['price'] for item in price_history],
            'open': [item['price'] * random.uniform(0.98, 1.02) for item in price_history],
            'high': [item['price'] * random.uniform(1.0, 1.05) for item in price_history],
            'low': [item['price'] * random.uniform(0.95, 1.0) for item in price_history],
            'vol': [item['volume'] / 100 for item in price_history],  # tushare中vol是手数
        })

        return {
            'symbol': symbol,
            'ts_code': ts_code,
            'company_name': company_name,
            'current_price': current_price,
            'price_change': price_change,
            'price_change_percent': price_change_percent,
            'volume': random.randint(1000000, 50000000),
            'market': '主板',
            'industry': '金融业',
            'area': '深圳',
            'price_history': price_history,
            'raw_data': mock_df,
            'timestamp': datetime.now().isoformat()
        }

    async def calculate_indicators(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算技术指标"""
        try:
            df = stock_data['raw_data'].copy()

            # 确保数据列名正确
            if 'close' not in df.columns:
                logger.error("数据中缺少close列")
                return self._get_default_indicators()

            if not TA_AVAILABLE:
                logger.warning("ta库未安装，使用简化指标计算")
                return self._calculate_simple_indicators(df)

            # RSI (相对强弱指数)
            try:
                rsi = ta.momentum.RSIIndicator(df['close']).rsi().iloc[-1]
                rsi_signal = self._get_rsi_signal(rsi)
            except Exception as e:
                logger.warning(f"RSI计算失败: {e}")
                rsi_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': 'RSI计算失败'}

            # MACD
            try:
                macd_line = ta.trend.MACD(df['close']).macd().iloc[-1]
                macd_signal_line = ta.trend.MACD(df['close']).macd_signal().iloc[-1]
                macd_histogram = ta.trend.MACD(df['close']).macd_diff().iloc[-1]
                macd_signal = self._get_macd_signal(macd_line, macd_signal_line, macd_histogram)
            except Exception as e:
                logger.warning(f"MACD计算失败: {e}")
                macd_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': 'MACD计算失败'}

            # 移动平均线
            try:
                ma_5 = ta.trend.SMAIndicator(df['close'], window=5).sma_indicator().iloc[-1]
                ma_20 = ta.trend.SMAIndicator(df['close'], window=20).sma_indicator().iloc[-1]
                current_price = df['close'].iloc[-1]
                ma_signal = self._get_ma_signal(current_price, ma_5, ma_20)
            except Exception as e:
                logger.warning(f"移动平均线计算失败: {e}")
                ma_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '移动平均线计算失败'}

            # 布林带
            try:
                bb_upper = ta.volatility.BollingerBands(df['close']).bollinger_hband().iloc[-1]
                bb_lower = ta.volatility.BollingerBands(df['close']).bollinger_lband().iloc[-1]
                bb_middle = ta.volatility.BollingerBands(df['close']).bollinger_mavg().iloc[-1]
                bb_signal = self._get_bollinger_signal(current_price, bb_upper, bb_lower, bb_middle)
            except Exception as e:
                logger.warning(f"布林带计算失败: {e}")
                bb_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '布林带计算失败'}

            # 成交量分析
            if 'vol' in df.columns:
                try:
                    volume_ma = df['vol'].rolling(20).mean().iloc[-1]
                    current_volume = df['vol'].iloc[-1]
                    volume_signal = self._get_volume_signal(current_volume, volume_ma)
                except Exception as e:
                    logger.warning(f"成交量分析失败: {e}")
                    volume_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '成交量分析失败'}
            else:
                volume_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '成交量数据不可用'}

            # 动量指标
            try:
                momentum = ta.momentum.ROCIndicator(df['close']).roc().iloc[-1]
                momentum_signal = self._get_momentum_signal(momentum)
            except Exception as e:
                logger.warning(f"动量指标计算失败: {e}")
                momentum_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '动量指标计算失败'}

            return {
                'rsi': {
                    'name': 'RSI',
                    'value': float(rsi) if not pd.isna(rsi) else 50.0,
                    'signal': rsi_signal['signal'],
                    'strength': rsi_signal['strength'],
                    'description': rsi_signal['description']
                },
                'macd': {
                    'name': 'MACD',
                    'value': float(macd_line) if not pd.isna(macd_line) else 0.0,
                    'signal': macd_signal['signal'],
                    'strength': macd_signal['strength'],
                    'description': macd_signal['description']
                },
                'ma': {
                    'name': '移动平均',
                    'value': float(ma_5) if not pd.isna(ma_5) else float(current_price),
                    'signal': ma_signal['signal'],
                    'strength': ma_signal['strength'],
                    'description': ma_signal['description']
                },
                'bollinger': {
                    'name': '布林带',
                    'value': float(bb_middle) if not pd.isna(bb_middle) else float(current_price),
                    'signal': bb_signal['signal'],
                    'strength': bb_signal['strength'],
                    'description': bb_signal['description']
                },
                'volume': {
                    'name': '成交量',
                    'value': float(current_volume) if 'vol' in df.columns and not pd.isna(current_volume) else 0.0,
                    'signal': volume_signal['signal'],
                    'strength': volume_signal['strength'],
                    'description': volume_signal['description']
                },
                'momentum': {
                    'name': '动量',
                    'value': float(momentum) if not pd.isna(momentum) else 0.0,
                    'signal': momentum_signal['signal'],
                    'strength': momentum_signal['strength'],
                    'description': momentum_signal['description']
                }
            }

        except Exception as e:
            logger.error(f"计算技术指标失败: {str(e)}")
            return self._get_default_indicators()

    async def calculate_detailed_indicators(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算详细技术指标分析"""
        try:
            df = stock_data['raw_data'].copy()
            current_price = df['close'].iloc[-1]

            # 基础指标
            basic_indicators = await self.calculate_indicators(stock_data)

            # 详细指标计算
            detailed = {}

            # 1. RSI详细分析
            if len(df) >= 14:
                rsi_values = self._calculate_rsi_series(df['close'])
                rsi_current = rsi_values.iloc[-1] if not pd.isna(rsi_values.iloc[-1]) else 50.0
                rsi_prev = rsi_values.iloc[-2] if len(rsi_values) > 1 and not pd.isna(rsi_values.iloc[-2]) else rsi_current

                detailed['rsi'] = {
                    'name': 'RSI相对强弱指数',
                    'current_value': round(rsi_current, 2),
                    'previous_value': round(rsi_prev, 2),
                    'change': round(rsi_current - rsi_prev, 2),
                    'level': self._get_rsi_level(rsi_current),
                    'signal': basic_indicators['rsi']['signal'],
                    'strength': basic_indicators['rsi']['strength'],
                    'description': basic_indicators['rsi']['description'],
                    'interpretation': self._interpret_rsi(rsi_current, rsi_prev),
                    'historical_range': {
                        'min': round(rsi_values.min(), 2),
                        'max': round(rsi_values.max(), 2),
                        'avg': round(rsi_values.mean(), 2)
                    }
                }

            # 2. MACD详细分析
            if len(df) >= 26:
                macd_data = self._calculate_macd_series(df['close'])
                detailed['macd'] = {
                    'name': 'MACD指标',
                    'macd_line': round(macd_data['macd'].iloc[-1], 4),
                    'signal_line': round(macd_data['signal'].iloc[-1], 4),
                    'histogram': round(macd_data['histogram'].iloc[-1], 4),
                    'histogram_change': round(macd_data['histogram'].iloc[-1] - macd_data['histogram'].iloc[-2], 4) if len(macd_data) > 1 else 0,
                    'signal': basic_indicators['macd']['signal'],
                    'strength': basic_indicators['macd']['strength'],
                    'description': basic_indicators['macd']['description'],
                    'interpretation': self._interpret_macd(macd_data),
                    'trend': 'BULLISH' if macd_data['macd'].iloc[-1] > macd_data['signal'].iloc[-1] else 'BEARISH'
                }

            # 3. 移动平均详细分析
            ma_data = self._calculate_ma_series(df['close'])
            detailed['moving_averages'] = {
                'name': '移动平均线',
                'ma5': round(ma_data['ma5'].iloc[-1], 2),
                'ma10': round(ma_data['ma10'].iloc[-1], 2),
                'ma20': round(ma_data['ma20'].iloc[-1], 2),
                'ma50': round(ma_data['ma50'].iloc[-1], 2) if len(df) >= 50 else None,
                'current_price': round(current_price, 2),
                'price_vs_ma5': round(((current_price - ma_data['ma5'].iloc[-1]) / ma_data['ma5'].iloc[-1]) * 100, 2),
                'price_vs_ma20': round(((current_price - ma_data['ma20'].iloc[-1]) / ma_data['ma20'].iloc[-1]) * 100, 2),
                'signal': basic_indicators['ma']['signal'],
                'strength': basic_indicators['ma']['strength'],
                'description': basic_indicators['ma']['description'],
                'interpretation': self._interpret_ma(current_price, ma_data),
                'trend_strength': self._calculate_ma_trend_strength(ma_data)
            }

            # 4. 布林带详细分析
            if len(df) >= 20:
                bb_data = self._calculate_bollinger_series(df['close'])
                bb_position = self._calculate_bb_position(current_price, bb_data)
                detailed['bollinger_bands'] = {
                    'name': '布林带',
                    'upper_band': round(bb_data['upper'].iloc[-1], 2),
                    'middle_band': round(bb_data['middle'].iloc[-1], 2),
                    'lower_band': round(bb_data['lower'].iloc[-1], 2),
                    'current_price': round(current_price, 2),
                    'position': bb_position,
                    'width': round(((bb_data['upper'].iloc[-1] - bb_data['lower'].iloc[-1]) / bb_data['middle'].iloc[-1]) * 100, 2),
                    'signal': basic_indicators['bollinger']['signal'],
                    'strength': basic_indicators['bollinger']['strength'],
                    'description': basic_indicators['bollinger']['description'],
                    'interpretation': self._interpret_bollinger(current_price, bb_data, bb_position)
                }

            # 5. 成交量详细分析
            if 'vol' in df.columns:
                volume_data = self._calculate_volume_analysis(df)
                detailed['volume'] = {
                    'name': '成交量分析',
                    'current_volume': int(volume_data['current']),
                    'avg_volume_5': int(volume_data['avg_5']),
                    'avg_volume_20': int(volume_data['avg_20']),
                    'volume_ratio': round(volume_data['ratio'], 2),
                    'volume_trend': volume_data['trend'],
                    'signal': basic_indicators['volume']['signal'],
                    'strength': basic_indicators['volume']['strength'],
                    'description': basic_indicators['volume']['description'],
                    'interpretation': self._interpret_volume(volume_data)
                }

            # 6. 动量指标详细分析
            momentum_data = self._calculate_momentum_analysis(df['close'])
            detailed['momentum'] = {
                'name': '动量指标',
                'roc_5': round(momentum_data['roc_5'], 2),
                'roc_10': round(momentum_data['roc_10'], 2),
                'roc_20': round(momentum_data['roc_20'], 2),
                'momentum_score': momentum_data['score'],
                'signal': basic_indicators['momentum']['signal'],
                'strength': basic_indicators['momentum']['strength'],
                'description': basic_indicators['momentum']['description'],
                'interpretation': self._interpret_momentum(momentum_data)
            }

            # 7. 波动率分析
            volatility_data = self._calculate_volatility_analysis(df['close'])
            detailed['volatility'] = {
                'name': '波动率分析',
                'daily_volatility': round(volatility_data['daily'], 4),
                'weekly_volatility': round(volatility_data['weekly'], 4),
                'monthly_volatility': round(volatility_data['monthly'], 4),
                'volatility_level': volatility_data['level'],
                'interpretation': self._interpret_volatility(volatility_data)
            }

            return detailed

        except Exception as e:
            logger.error(f"详细指标计算失败: {str(e)}")
            return self._get_default_detailed_indicators()

    async def calculate_overall_score(self, detailed_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """计算综合评分"""
        try:
            scores = {}
            weights = {
                'rsi': 1.2,
                'macd': 1.5,
                'moving_averages': 1.3,
                'bollinger_bands': 1.0,
                'volume': 0.8,
                'momentum': 1.1
            }

            total_score = 0
            total_weight = 0
            signal_counts = {'BUY': 0, 'SELL': 0, 'NEUTRAL': 0}

            for indicator_name, weight in weights.items():
                if indicator_name in detailed_indicators:
                    indicator = detailed_indicators[indicator_name]
                    signal = indicator.get('signal', 'NEUTRAL')
                    strength = indicator.get('strength', 'WEAK')

                    # 计算单项得分
                    signal_score = {'BUY': 1, 'NEUTRAL': 0, 'SELL': -1}.get(signal, 0)
                    strength_multiplier = {'STRONG': 1.0, 'MODERATE': 0.7, 'WEAK': 0.4}.get(strength, 0.4)

                    item_score = signal_score * strength_multiplier * weight
                    scores[indicator_name] = {
                        'signal': signal,
                        'strength': strength,
                        'score': round(item_score, 2),
                        'weight': weight
                    }

                    total_score += item_score
                    total_weight += weight
                    signal_counts[signal] += 1

            # 计算综合得分 (-100 到 100)
            if total_weight > 0:
                overall_score = (total_score / total_weight) * 100
            else:
                overall_score = 0

            # 确定综合信号
            if overall_score > 30:
                overall_signal = 'BUY'
                overall_strength = 'STRONG' if overall_score > 60 else 'MODERATE'
            elif overall_score < -30:
                overall_signal = 'SELL'
                overall_strength = 'STRONG' if overall_score < -60 else 'MODERATE'
            else:
                overall_signal = 'NEUTRAL'
                overall_strength = 'WEAK'

            return {
                'overall_score': round(overall_score, 1),
                'overall_signal': overall_signal,
                'overall_strength': overall_strength,
                'signal_distribution': signal_counts,
                'individual_scores': scores,
                'interpretation': self._interpret_overall_score(overall_score, overall_signal),
                'confidence_level': self._calculate_confidence_level(signal_counts, overall_score)
            }

        except Exception as e:
            logger.error(f"综合评分计算失败: {str(e)}")
            return {
                'overall_score': 0,
                'overall_signal': 'NEUTRAL',
                'overall_strength': 'WEAK',
                'signal_distribution': {'BUY': 0, 'SELL': 0, 'NEUTRAL': 0},
                'individual_scores': {},
                'interpretation': '评分计算失败',
                'confidence_level': 'LOW'
            }

    async def calculate_indicator_trends(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算指标趋势"""
        try:
            df = stock_data['raw_data'].copy()
            trends = {}

            # RSI趋势
            if len(df) >= 14:
                rsi_series = self._calculate_rsi_series(df['close'])
                trends['rsi'] = self._analyze_trend(rsi_series.tail(10))

            # MACD趋势
            if len(df) >= 26:
                macd_data = self._calculate_macd_series(df['close'])
                trends['macd'] = self._analyze_trend(macd_data['histogram'].tail(10))

            # 价格趋势
            trends['price'] = self._analyze_trend(df['close'].tail(20))

            # 成交量趋势
            if 'vol' in df.columns:
                trends['volume'] = self._analyze_trend(df['vol'].tail(10))

            return trends

        except Exception as e:
            logger.error(f"趋势分析失败: {str(e)}")
            return {}

    async def analyze_signal_strength(self, detailed_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """分析信号强度"""
        try:
            signal_analysis = {
                'strong_signals': [],
                'moderate_signals': [],
                'weak_signals': [],
                'conflicting_signals': [],
                'consensus_level': 'LOW'
            }

            buy_signals = []
            sell_signals = []
            neutral_signals = []

            for name, indicator in detailed_indicators.items():
                signal = indicator.get('signal', 'NEUTRAL')
                strength = indicator.get('strength', 'WEAK')

                signal_info = {
                    'indicator': name,
                    'signal': signal,
                    'strength': strength,
                    'description': indicator.get('description', '')
                }

                if strength == 'STRONG':
                    signal_analysis['strong_signals'].append(signal_info)
                elif strength == 'MODERATE':
                    signal_analysis['moderate_signals'].append(signal_info)
                else:
                    signal_analysis['weak_signals'].append(signal_info)

                if signal == 'BUY':
                    buy_signals.append(signal_info)
                elif signal == 'SELL':
                    sell_signals.append(signal_info)
                else:
                    neutral_signals.append(signal_info)

            # 检测冲突信号
            if len(buy_signals) > 0 and len(sell_signals) > 0:
                signal_analysis['conflicting_signals'] = buy_signals + sell_signals

            # 计算共识水平
            total_signals = len(buy_signals) + len(sell_signals) + len(neutral_signals)
            if total_signals > 0:
                max_consensus = max(len(buy_signals), len(sell_signals), len(neutral_signals))
                consensus_ratio = max_consensus / total_signals

                if consensus_ratio >= 0.8:
                    signal_analysis['consensus_level'] = 'HIGH'
                elif consensus_ratio >= 0.6:
                    signal_analysis['consensus_level'] = 'MEDIUM'
                else:
                    signal_analysis['consensus_level'] = 'LOW'

            signal_analysis['signal_counts'] = {
                'buy': len(buy_signals),
                'sell': len(sell_signals),
                'neutral': len(neutral_signals)
            }

            return signal_analysis

        except Exception as e:
            logger.error(f"信号强度分析失败: {str(e)}")
            return {
                'strong_signals': [],
                'moderate_signals': [],
                'weak_signals': [],
                'conflicting_signals': [],
                'consensus_level': 'LOW',
                'signal_counts': {'buy': 0, 'sell': 0, 'neutral': 0}
            }

    def _calculate_simple_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """简化的技术指标计算（不依赖ta库）"""
        try:
            current_price = df['close'].iloc[-1]

            # 简单移动平均
            ma_5 = df['close'].rolling(5).mean().iloc[-1] if len(df) >= 5 else current_price
            ma_20 = df['close'].rolling(20).mean().iloc[-1] if len(df) >= 20 else current_price

            # 简单RSI计算
            if len(df) >= 14:
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                rsi_value = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
            else:
                rsi_value = 50.0

            # 简单动量计算
            if len(df) >= 10:
                momentum = ((current_price - df['close'].iloc[-10]) / df['close'].iloc[-10]) * 100
            else:
                momentum = 0.0

            return {
                'rsi': {
                    'name': 'RSI',
                    'value': float(rsi_value),
                    'signal': self._get_rsi_signal(rsi_value)['signal'],
                    'strength': self._get_rsi_signal(rsi_value)['strength'],
                    'description': self._get_rsi_signal(rsi_value)['description']
                },
                'macd': {
                    'name': 'MACD',
                    'value': 0.0,
                    'signal': 'NEUTRAL',
                    'strength': 'WEAK',
                    'description': 'MACD计算需要ta库支持'
                },
                'ma': {
                    'name': '移动平均',
                    'value': float(ma_5),
                    'signal': self._get_ma_signal(current_price, ma_5, ma_20)['signal'],
                    'strength': self._get_ma_signal(current_price, ma_5, ma_20)['strength'],
                    'description': self._get_ma_signal(current_price, ma_5, ma_20)['description']
                },
                'bollinger': {
                    'name': '布林带',
                    'value': float(current_price),
                    'signal': 'NEUTRAL',
                    'strength': 'WEAK',
                    'description': '布林带计算需要ta库支持'
                },
                'volume': {
                    'name': '成交量',
                    'value': float(df['vol'].iloc[-1]) if 'vol' in df.columns else 0.0,
                    'signal': 'NEUTRAL',
                    'strength': 'WEAK',
                    'description': '成交量分析简化版'
                },
                'momentum': {
                    'name': '动量',
                    'value': float(momentum),
                    'signal': self._get_momentum_signal(momentum)['signal'],
                    'strength': self._get_momentum_signal(momentum)['strength'],
                    'description': self._get_momentum_signal(momentum)['description']
                }
            }
        except Exception as e:
            logger.error(f"简化指标计算失败: {str(e)}")
            return self._get_default_indicators()

    def _get_default_indicators(self) -> Dict[str, Any]:
        """获取默认技术指标"""
        return {
            'rsi': {
                'name': 'RSI',
                'value': 50.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'RSI数据不可用'
            },
            'macd': {
                'name': 'MACD',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'MACD数据不可用'
            },
            'ma': {
                'name': '移动平均',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '移动平均数据不可用'
            },
            'bollinger': {
                'name': '布林带',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '布林带数据不可用'
            },
            'volume': {
                'name': '成交量',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量数据不可用'
            },
            'momentum': {
                'name': '动量',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '动量数据不可用'
            }
        }

    def _get_rsi_signal(self, rsi: float) -> Dict[str, str]:
        """RSI信号分析"""
        if pd.isna(rsi):
            rsi = 50.0

        if rsi > 70:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if rsi > 80 else 'MODERATE',
                'description': f'RSI={rsi:.1f}，超买区域，考虑减仓'
            }
        elif rsi < 30:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if rsi < 20 else 'MODERATE',
                'description': f'RSI={rsi:.1f}，超卖区域，考虑买入'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': f'RSI={rsi:.1f}，处于正常区间'
            }

    def _get_macd_signal(self, macd_line: float, signal_line: float, histogram: float) -> Dict[str, str]:
        """MACD信号分析"""
        if any(pd.isna([macd_line, signal_line, histogram])):
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'MACD数据不足'
            }

        if macd_line > signal_line and histogram > 0:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if histogram > 0.5 else 'MODERATE',
                'description': 'MACD金叉，上涨趋势'
            }
        elif macd_line < signal_line and histogram < 0:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if histogram < -0.5 else 'MODERATE',
                'description': 'MACD死叉，下跌趋势'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'MACD信号不明确'
            }

    def _get_ma_signal(self, price: float, ma_5: float, ma_20: float) -> Dict[str, str]:
        """移动平均线信号分析"""
        if any(pd.isna([price, ma_5, ma_20])):
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '均线数据不足'
            }

        if price > ma_5 > ma_20:
            return {
                'signal': 'BUY',
                'strength': 'STRONG',
                'description': '价格突破短期均线，多头排列'
            }
        elif price < ma_5 < ma_20:
            return {
                'signal': 'SELL',
                'strength': 'STRONG',
                'description': '价格跌破短期均线，空头排列'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'MODERATE',
                'description': '价格在均线附近震荡'
            }

    def _get_bollinger_signal(self, price: float, upper: float, lower: float, middle: float) -> Dict[str, str]:
        """布林带信号分析"""
        if any(pd.isna([price, upper, lower, middle])):
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '布林带数据不足'
            }

        if price > upper:
            return {
                'signal': 'SELL',
                'strength': 'MODERATE',
                'description': '价格突破上轨，可能回调'
            }
        elif price < lower:
            return {
                'signal': 'BUY',
                'strength': 'MODERATE',
                'description': '价格跌破下轨，可能反弹'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '价格在布林带内正常波动'
            }

    def _get_volume_signal(self, current_volume: float, avg_volume: float) -> Dict[str, str]:
        """成交量信号分析"""
        if pd.isna(current_volume) or pd.isna(avg_volume) or avg_volume == 0:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量数据不足'
            }

        volume_ratio = current_volume / avg_volume
        if volume_ratio > 1.5:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if volume_ratio > 2 else 'MODERATE',
                'description': f'成交量放大{volume_ratio:.1f}倍，关注度提升'
            }
        elif volume_ratio < 0.5:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量萎缩，市场观望'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量正常'
            }

    def _get_momentum_signal(self, momentum: float) -> Dict[str, str]:
        """动量信号分析"""
        if pd.isna(momentum):
            momentum = 0.0

        if momentum > 5:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if momentum > 10 else 'MODERATE',
                'description': f'动量强劲({momentum:.1f}%)，上涨趋势'
            }
        elif momentum < -5:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if momentum < -10 else 'MODERATE',
                'description': f'动量疲弱({momentum:.1f}%)，下跌趋势'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '动量平稳'
            }

    # 多指标分析的辅助方法
    def _calculate_rsi_series(self, prices: pd.Series) -> pd.Series:
        """计算RSI序列"""
        try:
            if TA_AVAILABLE:
                return ta.momentum.RSIIndicator(prices).rsi()
            else:
                # 简化RSI计算
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
                rs = gain / loss
                return 100 - (100 / (1 + rs))
        except Exception:
            return pd.Series([50.0] * len(prices), index=prices.index)

    def _calculate_macd_series(self, prices: pd.Series) -> Dict[str, pd.Series]:
        """计算MACD序列"""
        try:
            if TA_AVAILABLE:
                macd_indicator = ta.trend.MACD(prices)
                return {
                    'macd': macd_indicator.macd(),
                    'signal': macd_indicator.macd_signal(),
                    'histogram': macd_indicator.macd_diff()
                }
            else:
                # 简化MACD计算
                ema_12 = prices.ewm(span=12).mean()
                ema_26 = prices.ewm(span=26).mean()
                macd = ema_12 - ema_26
                signal = macd.ewm(span=9).mean()
                histogram = macd - signal
                return {
                    'macd': macd,
                    'signal': signal,
                    'histogram': histogram
                }
        except Exception:
            return {
                'macd': pd.Series([0.0] * len(prices), index=prices.index),
                'signal': pd.Series([0.0] * len(prices), index=prices.index),
                'histogram': pd.Series([0.0] * len(prices), index=prices.index)
            }

    def _calculate_ma_series(self, prices: pd.Series) -> Dict[str, pd.Series]:
        """计算移动平均序列"""
        try:
            return {
                'ma5': prices.rolling(5).mean(),
                'ma10': prices.rolling(10).mean(),
                'ma20': prices.rolling(20).mean(),
                'ma50': prices.rolling(50).mean() if len(prices) >= 50 else prices.rolling(len(prices)).mean()
            }
        except Exception:
            return {
                'ma5': prices,
                'ma10': prices,
                'ma20': prices,
                'ma50': prices
            }

    def _calculate_bollinger_series(self, prices: pd.Series) -> Dict[str, pd.Series]:
        """计算布林带序列"""
        try:
            if TA_AVAILABLE:
                bb = ta.volatility.BollingerBands(prices)
                return {
                    'upper': bb.bollinger_hband(),
                    'middle': bb.bollinger_mavg(),
                    'lower': bb.bollinger_lband()
                }
            else:
                # 简化布林带计算
                ma20 = prices.rolling(20).mean()
                std20 = prices.rolling(20).std()
                return {
                    'upper': ma20 + (std20 * 2),
                    'middle': ma20,
                    'lower': ma20 - (std20 * 2)
                }
        except Exception:
            return {
                'upper': prices,
                'middle': prices,
                'lower': prices
            }

    def _calculate_volume_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算成交量分析"""
        try:
            current_volume = df['vol'].iloc[-1]
            avg_5 = df['vol'].rolling(5).mean().iloc[-1]
            avg_20 = df['vol'].rolling(20).mean().iloc[-1]

            ratio = current_volume / avg_20 if avg_20 > 0 else 1.0

            # 判断成交量趋势
            if ratio > 1.5:
                trend = 'HIGH'
            elif ratio > 1.2:
                trend = 'ABOVE_AVERAGE'
            elif ratio < 0.8:
                trend = 'LOW'
            else:
                trend = 'NORMAL'

            return {
                'current': current_volume,
                'avg_5': avg_5,
                'avg_20': avg_20,
                'ratio': ratio,
                'trend': trend
            }
        except Exception:
            return {
                'current': 0,
                'avg_5': 0,
                'avg_20': 0,
                'ratio': 1.0,
                'trend': 'NORMAL'
            }

    def _calculate_momentum_analysis(self, prices: pd.Series) -> Dict[str, Any]:
        """计算动量分析"""
        try:
            current_price = prices.iloc[-1]

            # 计算不同周期的ROC
            roc_5 = ((current_price - prices.iloc[-6]) / prices.iloc[-6]) * 100 if len(prices) > 5 else 0
            roc_10 = ((current_price - prices.iloc[-11]) / prices.iloc[-11]) * 100 if len(prices) > 10 else 0
            roc_20 = ((current_price - prices.iloc[-21]) / prices.iloc[-21]) * 100 if len(prices) > 20 else 0

            # 计算动量得分
            score = (roc_5 * 0.5 + roc_10 * 0.3 + roc_20 * 0.2)

            return {
                'roc_5': roc_5,
                'roc_10': roc_10,
                'roc_20': roc_20,
                'score': score
            }
        except Exception:
            return {
                'roc_5': 0,
                'roc_10': 0,
                'roc_20': 0,
                'score': 0
            }

    def _calculate_volatility_analysis(self, prices: pd.Series) -> Dict[str, Any]:
        """计算波动率分析"""
        try:
            # 计算不同周期的波动率
            daily_returns = prices.pct_change().dropna()

            daily_vol = daily_returns.std()
            weekly_vol = daily_vol * (5 ** 0.5)  # 假设一周5个交易日
            monthly_vol = daily_vol * (20 ** 0.5)  # 假设一月20个交易日

            # 判断波动率水平
            if daily_vol > 0.03:
                level = 'HIGH'
            elif daily_vol > 0.02:
                level = 'MEDIUM'
            else:
                level = 'LOW'

            return {
                'daily': daily_vol,
                'weekly': weekly_vol,
                'monthly': monthly_vol,
                'level': level
            }
        except Exception:
            return {
                'daily': 0.02,
                'weekly': 0.045,
                'monthly': 0.09,
                'level': 'MEDIUM'
            }

    def _get_rsi_level(self, rsi: float) -> str:
        """获取RSI水平"""
        if rsi >= 70:
            return 'OVERBOUGHT'
        elif rsi <= 30:
            return 'OVERSOLD'
        elif rsi >= 60:
            return 'STRONG'
        elif rsi <= 40:
            return 'WEAK'
        else:
            return 'NEUTRAL'

    def _calculate_bb_position(self, price: float, bb_data: Dict[str, pd.Series]) -> str:
        """计算布林带位置"""
        try:
            upper = bb_data['upper'].iloc[-1]
            lower = bb_data['lower'].iloc[-1]
            middle = bb_data['middle'].iloc[-1]

            if price > upper:
                return 'ABOVE_UPPER'
            elif price < lower:
                return 'BELOW_LOWER'
            elif price > middle:
                return 'ABOVE_MIDDLE'
            else:
                return 'BELOW_MIDDLE'
        except Exception:
            return 'MIDDLE'

    def _calculate_ma_trend_strength(self, ma_data: Dict[str, pd.Series]) -> str:
        """计算移动平均趋势强度"""
        try:
            ma5 = ma_data['ma5'].iloc[-1]
            ma10 = ma_data['ma10'].iloc[-1]
            ma20 = ma_data['ma20'].iloc[-1]

            if ma5 > ma10 > ma20:
                return 'STRONG_UPTREND'
            elif ma5 < ma10 < ma20:
                return 'STRONG_DOWNTREND'
            elif ma5 > ma20:
                return 'WEAK_UPTREND'
            elif ma5 < ma20:
                return 'WEAK_DOWNTREND'
            else:
                return 'SIDEWAYS'
        except Exception:
            return 'SIDEWAYS'

    def _analyze_trend(self, series: pd.Series) -> Dict[str, Any]:
        """分析趋势"""
        try:
            if len(series) < 3:
                return {'direction': 'UNKNOWN', 'strength': 'WEAK'}

            # 计算趋势斜率
            x = range(len(series))
            y = series.values
            slope = np.polyfit(x, y, 1)[0]

            # 计算R²
            correlation = np.corrcoef(x, y)[0, 1]
            r_squared = correlation ** 2

            # 判断趋势方向
            if slope > 0:
                direction = 'UP'
            elif slope < 0:
                direction = 'DOWN'
            else:
                direction = 'SIDEWAYS'

            # 判断趋势强度
            if r_squared > 0.8:
                strength = 'STRONG'
            elif r_squared > 0.5:
                strength = 'MODERATE'
            else:
                strength = 'WEAK'

            return {
                'direction': direction,
                'strength': strength,
                'slope': slope,
                'r_squared': r_squared
            }
        except Exception:
            return {'direction': 'UNKNOWN', 'strength': 'WEAK', 'slope': 0, 'r_squared': 0}

    # 解释方法
    def _interpret_rsi(self, current: float, previous: float) -> str:
        """解释RSI"""
        level = self._get_rsi_level(current)
        change = current - previous

        if level == 'OVERBOUGHT':
            return f"RSI处于超买区域({current:.1f})，可能面临回调压力"
        elif level == 'OVERSOLD':
            return f"RSI处于超卖区域({current:.1f})，可能出现反弹机会"
        elif change > 5:
            return f"RSI快速上升至{current:.1f}，买入动能增强"
        elif change < -5:
            return f"RSI快速下降至{current:.1f}，卖出压力增加"
        else:
            return f"RSI为{current:.1f}，处于{level.lower()}状态"

    def _interpret_macd(self, macd_data: Dict[str, pd.Series]) -> str:
        """解释MACD"""
        try:
            macd = macd_data['macd'].iloc[-1]
            signal = macd_data['signal'].iloc[-1]
            histogram = macd_data['histogram'].iloc[-1]

            if macd > signal and histogram > 0:
                return "MACD金叉且柱状图为正，上涨趋势确立"
            elif macd < signal and histogram < 0:
                return "MACD死叉且柱状图为负，下跌趋势确立"
            elif macd > signal:
                return "MACD线高于信号线，短期看涨"
            else:
                return "MACD线低于信号线，短期看跌"
        except Exception:
            return "MACD信号不明确"

    def _interpret_ma(self, price: float, ma_data: Dict[str, pd.Series]) -> str:
        """解释移动平均"""
        try:
            ma5 = ma_data['ma5'].iloc[-1]
            ma20 = ma_data['ma20'].iloc[-1]

            if price > ma5 > ma20:
                return f"价格({price:.2f})高于短期和长期均线，多头排列"
            elif price < ma5 < ma20:
                return f"价格({price:.2f})低于短期和长期均线，空头排列"
            elif price > ma20:
                return f"价格({price:.2f})高于长期均线，总体趋势向上"
            else:
                return f"价格({price:.2f})低于长期均线，总体趋势向下"
        except Exception:
            return "移动平均信号不明确"

    def _interpret_bollinger(self, price: float, bb_data: Dict[str, pd.Series], position: str) -> str:
        """解释布林带"""
        try:
            if position == 'ABOVE_UPPER':
                return f"价格突破布林带上轨，可能超买"
            elif position == 'BELOW_LOWER':
                return f"价格跌破布林带下轨，可能超卖"
            elif position == 'ABOVE_MIDDLE':
                return f"价格位于布林带上半部，相对强势"
            else:
                return f"价格位于布林带下半部，相对弱势"
        except Exception:
            return "布林带信号不明确"

    def _interpret_volume(self, volume_data: Dict[str, Any]) -> str:
        """解释成交量"""
        trend = volume_data['trend']
        ratio = volume_data['ratio']

        if trend == 'HIGH':
            return f"成交量放大({ratio:.1f}倍)，市场关注度高"
        elif trend == 'ABOVE_AVERAGE':
            return f"成交量略高于平均({ratio:.1f}倍)，交投活跃"
        elif trend == 'LOW':
            return f"成交量萎缩({ratio:.1f}倍)，市场观望情绪浓厚"
        else:
            return f"成交量正常({ratio:.1f}倍)，市场交投平稳"

    def _interpret_momentum(self, momentum_data: Dict[str, Any]) -> str:
        """解释动量"""
        score = momentum_data['score']

        if score > 5:
            return f"动量强劲({score:.1f}%)，上涨趋势明显"
        elif score > 2:
            return f"动量偏强({score:.1f}%)，短期看涨"
        elif score < -5:
            return f"动量疲弱({score:.1f}%)，下跌趋势明显"
        elif score < -2:
            return f"动量偏弱({score:.1f}%)，短期看跌"
        else:
            return f"动量平稳({score:.1f}%)，方向不明确"

    def _interpret_volatility(self, volatility_data: Dict[str, Any]) -> str:
        """解释波动率"""
        level = volatility_data['level']
        daily = volatility_data['daily']

        if level == 'HIGH':
            return f"波动率较高({daily:.1%})，风险和机会并存"
        elif level == 'MEDIUM':
            return f"波动率适中({daily:.1%})，市场相对稳定"
        else:
            return f"波动率较低({daily:.1%})，市场平静"

    def _interpret_overall_score(self, score: float, signal: str) -> str:
        """解释综合评分"""
        if signal == 'BUY':
            if score > 60:
                return f"综合评分{score:.1f}分，多项指标强烈看涨，建议积极买入"
            else:
                return f"综合评分{score:.1f}分，多数指标看涨，建议适量买入"
        elif signal == 'SELL':
            if score < -60:
                return f"综合评分{score:.1f}分，多项指标强烈看跌，建议及时卖出"
            else:
                return f"综合评分{score:.1f}分，多数指标看跌，建议减仓观望"
        else:
            return f"综合评分{score:.1f}分，指标信号分歧，建议保持观望"

    def _calculate_confidence_level(self, signal_counts: Dict[str, int], score: float) -> str:
        """计算置信度水平"""
        total = sum(signal_counts.values())
        if total == 0:
            return 'LOW'

        max_count = max(signal_counts.values())
        consensus_ratio = max_count / total

        if consensus_ratio >= 0.8 and abs(score) > 50:
            return 'HIGH'
        elif consensus_ratio >= 0.6 and abs(score) > 30:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _get_default_detailed_indicators(self) -> Dict[str, Any]:
        """获取默认详细指标"""
        return {
            'rsi': {
                'name': 'RSI相对强弱指数',
                'current_value': 50.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'RSI数据不可用'
            },
            'macd': {
                'name': 'MACD指标',
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'MACD数据不可用'
            },
            'moving_averages': {
                'name': '移动平均线',
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '移动平均数据不可用'
            },
            'bollinger_bands': {
                'name': '布林带',
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '布林带数据不可用'
            },
            'volume': {
                'name': '成交量分析',
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量数据不可用'
            },
            'momentum': {
                'name': '动量指标',
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '动量数据不可用'
            },
            'volatility': {
                'name': '波动率分析',
                'interpretation': '波动率数据不可用'
            }
        }
