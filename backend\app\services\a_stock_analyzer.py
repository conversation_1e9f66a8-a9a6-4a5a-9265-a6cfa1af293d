try:
    import tushare as ts
    TUSHARE_AVAILABLE = True
except ImportError:
    TUSHARE_AVAILABLE = False

import pandas as pd
import numpy as np
try:
    import ta
    TA_AVAILABLE = True
except ImportError:
    TA_AVAILABLE = False

from typing import Dict, List, Optional, Any
import logging
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)

class AStockAnalyzer:
    """A股数据分析器"""

    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        # 初始化tushare
        if TUSHARE_AVAILABLE:
            try:
                # 这里需要设置tushare token
                # ts.set_token('your_token_here')  # 需要用户自己申请token
                self.pro = ts.pro_api()
                logger.info("Tushare初始化成功")
            except Exception as e:
                logger.warning(f"Tushare初始化失败: {e}, 将使用模拟数据")
                self.pro = None
        else:
            logger.warning("Tushare未安装，将使用模拟数据")
            self.pro = None

    def _normalize_stock_code(self, symbol: str) -> str:
        """标准化股票代码"""
        # 移除空格和特殊字符
        symbol = re.sub(r'[^\w]', '', symbol.upper())

        # 如果是6位数字，自动添加后缀
        if symbol.isdigit() and len(symbol) == 6:
            if symbol.startswith('6'):
                return f"{symbol}.SH"  # 上交所
            elif symbol.startswith(('0', '3')):
                return f"{symbol}.SZ"  # 深交所

        # 如果已经有后缀，直接返回
        if '.' in symbol:
            return symbol

        # 默认处理
        return symbol

    async def get_stock_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取A股数据"""
        try:
            # 标准化股票代码
            ts_code = self._normalize_stock_code(symbol)

            # 检查缓存
            cache_key = f"a_stock_data:{ts_code}"
            if cache_key in self.cache:
                cached_time, data = self.cache[cache_key]
                if (datetime.now() - cached_time).seconds < self.cache_timeout:
                    return data

            if self.pro is None:
                # 使用模拟数据
                return self._get_mock_data(ts_code)

            # 获取股票基本信息
            stock_basic = self.pro.stock_basic(ts_code=ts_code, fields='ts_code,symbol,name,area,industry,market,list_date')
            if stock_basic.empty:
                logger.warning(f"未找到股票信息: {ts_code}")
                return self._get_mock_data(ts_code)

            stock_info = stock_basic.iloc[0]

            # 获取历史数据 (最近1年)
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')

            hist_data = self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
            if hist_data.empty:
                logger.warning(f"未找到历史数据: {ts_code}")
                return self._get_mock_data(ts_code)

            # 按日期排序
            hist_data = hist_data.sort_values('trade_date')
            hist_data.reset_index(drop=True, inplace=True)

            # 获取最新价格
            latest_data = hist_data.iloc[-1]
            current_price = latest_data['close']

            # 计算价格变化
            if len(hist_data) > 1:
                prev_price = hist_data.iloc[-2]['close']
                price_change = current_price - prev_price
                price_change_percent = (price_change / prev_price) * 100
            else:
                price_change = 0
                price_change_percent = 0

            # 准备价格历史数据
            price_history = []
            recent_data = hist_data.tail(60)  # 最近60天
            for _, row in recent_data.iterrows():
                price_history.append({
                    'date': pd.to_datetime(row['trade_date']).strftime('%Y-%m-%d'),
                    'price': float(row['close']),
                    'volume': int(row['vol'] * 100)  # 转换为股数
                })

            # 构建结果
            result = {
                'symbol': symbol,
                'ts_code': ts_code,
                'company_name': stock_info['name'],
                'current_price': float(current_price),
                'price_change': float(price_change),
                'price_change_percent': float(price_change_percent),
                'volume': int(latest_data['vol'] * 100),
                'market': stock_info['market'],
                'industry': stock_info['industry'],
                'area': stock_info['area'],
                'price_history': price_history,
                'raw_data': hist_data,
                'timestamp': datetime.now().isoformat()
            }

            # 缓存结果
            self.cache[cache_key] = (datetime.now(), result)

            logger.info(f"获取A股数据成功: {ts_code}")
            return result

        except Exception as e:
            logger.error(f"获取A股数据失败 {symbol}: {str(e)}")
            return self._get_mock_data(symbol)

    def _get_mock_data(self, symbol: str) -> Dict[str, Any]:
        """生成模拟A股数据"""
        import random

        # 模拟股票信息
        mock_names = {
            '000001.SZ': '平安银行',
            '000002.SZ': '万科A',
            '600000.SH': '浦发银行',
            '600036.SH': '招商银行',
            '600519.SH': '贵州茅台',
            '000858.SZ': '五粮液',
            '002415.SZ': '海康威视',
            '300059.SZ': '东方财富'
        }

        ts_code = self._normalize_stock_code(symbol)
        company_name = mock_names.get(ts_code, f"模拟股票{symbol}")

        # 生成模拟价格数据
        base_price = random.uniform(10, 200)
        current_price = base_price * random.uniform(0.95, 1.05)
        prev_price = base_price * random.uniform(0.95, 1.05)
        price_change = current_price - prev_price
        price_change_percent = (price_change / prev_price) * 100

        # 生成历史数据
        price_history = []
        for i in range(60):
            date = (datetime.now() - timedelta(days=59-i)).strftime('%Y-%m-%d')
            price = base_price * random.uniform(0.9, 1.1)
            volume = random.randint(1000000, 50000000)
            price_history.append({
                'date': date,
                'price': price,
                'volume': volume
            })

        # 创建模拟DataFrame
        mock_df = pd.DataFrame({
            'trade_date': [item['date'].replace('-', '') for item in price_history],
            'close': [item['price'] for item in price_history],
            'open': [item['price'] * random.uniform(0.98, 1.02) for item in price_history],
            'high': [item['price'] * random.uniform(1.0, 1.05) for item in price_history],
            'low': [item['price'] * random.uniform(0.95, 1.0) for item in price_history],
            'vol': [item['volume'] / 100 for item in price_history],  # tushare中vol是手数
        })

        return {
            'symbol': symbol,
            'ts_code': ts_code,
            'company_name': company_name,
            'current_price': current_price,
            'price_change': price_change,
            'price_change_percent': price_change_percent,
            'volume': random.randint(1000000, 50000000),
            'market': '主板',
            'industry': '金融业',
            'area': '深圳',
            'price_history': price_history,
            'raw_data': mock_df,
            'timestamp': datetime.now().isoformat()
        }

    async def calculate_indicators(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算技术指标"""
        try:
            df = stock_data['raw_data'].copy()

            # 确保数据列名正确
            if 'close' not in df.columns:
                logger.error("数据中缺少close列")
                return self._get_default_indicators()

            if not TA_AVAILABLE:
                logger.warning("ta库未安装，使用简化指标计算")
                return self._calculate_simple_indicators(df)

            # RSI (相对强弱指数)
            try:
                rsi = ta.momentum.RSIIndicator(df['close']).rsi().iloc[-1]
                rsi_signal = self._get_rsi_signal(rsi)
            except Exception as e:
                logger.warning(f"RSI计算失败: {e}")
                rsi_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': 'RSI计算失败'}

            # MACD
            try:
                macd_line = ta.trend.MACD(df['close']).macd().iloc[-1]
                macd_signal_line = ta.trend.MACD(df['close']).macd_signal().iloc[-1]
                macd_histogram = ta.trend.MACD(df['close']).macd_diff().iloc[-1]
                macd_signal = self._get_macd_signal(macd_line, macd_signal_line, macd_histogram)
            except Exception as e:
                logger.warning(f"MACD计算失败: {e}")
                macd_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': 'MACD计算失败'}

            # 移动平均线
            try:
                ma_5 = ta.trend.SMAIndicator(df['close'], window=5).sma_indicator().iloc[-1]
                ma_20 = ta.trend.SMAIndicator(df['close'], window=20).sma_indicator().iloc[-1]
                current_price = df['close'].iloc[-1]
                ma_signal = self._get_ma_signal(current_price, ma_5, ma_20)
            except Exception as e:
                logger.warning(f"移动平均线计算失败: {e}")
                ma_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '移动平均线计算失败'}

            # 布林带
            try:
                bb_upper = ta.volatility.BollingerBands(df['close']).bollinger_hband().iloc[-1]
                bb_lower = ta.volatility.BollingerBands(df['close']).bollinger_lband().iloc[-1]
                bb_middle = ta.volatility.BollingerBands(df['close']).bollinger_mavg().iloc[-1]
                bb_signal = self._get_bollinger_signal(current_price, bb_upper, bb_lower, bb_middle)
            except Exception as e:
                logger.warning(f"布林带计算失败: {e}")
                bb_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '布林带计算失败'}

            # 成交量分析
            if 'vol' in df.columns:
                try:
                    volume_ma = df['vol'].rolling(20).mean().iloc[-1]
                    current_volume = df['vol'].iloc[-1]
                    volume_signal = self._get_volume_signal(current_volume, volume_ma)
                except Exception as e:
                    logger.warning(f"成交量分析失败: {e}")
                    volume_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '成交量分析失败'}
            else:
                volume_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '成交量数据不可用'}

            # 动量指标
            try:
                momentum = ta.momentum.ROCIndicator(df['close']).roc().iloc[-1]
                momentum_signal = self._get_momentum_signal(momentum)
            except Exception as e:
                logger.warning(f"动量指标计算失败: {e}")
                momentum_signal = {'signal': 'NEUTRAL', 'strength': 'WEAK', 'description': '动量指标计算失败'}

            return {
                'rsi': {
                    'name': 'RSI',
                    'value': float(rsi) if not pd.isna(rsi) else 50.0,
                    'signal': rsi_signal['signal'],
                    'strength': rsi_signal['strength'],
                    'description': rsi_signal['description']
                },
                'macd': {
                    'name': 'MACD',
                    'value': float(macd_line) if not pd.isna(macd_line) else 0.0,
                    'signal': macd_signal['signal'],
                    'strength': macd_signal['strength'],
                    'description': macd_signal['description']
                },
                'ma': {
                    'name': '移动平均',
                    'value': float(ma_5) if not pd.isna(ma_5) else float(current_price),
                    'signal': ma_signal['signal'],
                    'strength': ma_signal['strength'],
                    'description': ma_signal['description']
                },
                'bollinger': {
                    'name': '布林带',
                    'value': float(bb_middle) if not pd.isna(bb_middle) else float(current_price),
                    'signal': bb_signal['signal'],
                    'strength': bb_signal['strength'],
                    'description': bb_signal['description']
                },
                'volume': {
                    'name': '成交量',
                    'value': float(current_volume) if 'vol' in df.columns and not pd.isna(current_volume) else 0.0,
                    'signal': volume_signal['signal'],
                    'strength': volume_signal['strength'],
                    'description': volume_signal['description']
                },
                'momentum': {
                    'name': '动量',
                    'value': float(momentum) if not pd.isna(momentum) else 0.0,
                    'signal': momentum_signal['signal'],
                    'strength': momentum_signal['strength'],
                    'description': momentum_signal['description']
                }
            }

        except Exception as e:
            logger.error(f"计算技术指标失败: {str(e)}")
            return self._get_default_indicators()

    def _calculate_simple_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """简化的技术指标计算（不依赖ta库）"""
        try:
            current_price = df['close'].iloc[-1]

            # 简单移动平均
            ma_5 = df['close'].rolling(5).mean().iloc[-1] if len(df) >= 5 else current_price
            ma_20 = df['close'].rolling(20).mean().iloc[-1] if len(df) >= 20 else current_price

            # 简单RSI计算
            if len(df) >= 14:
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                rsi_value = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
            else:
                rsi_value = 50.0

            # 简单动量计算
            if len(df) >= 10:
                momentum = ((current_price - df['close'].iloc[-10]) / df['close'].iloc[-10]) * 100
            else:
                momentum = 0.0

            return {
                'rsi': {
                    'name': 'RSI',
                    'value': float(rsi_value),
                    'signal': self._get_rsi_signal(rsi_value)['signal'],
                    'strength': self._get_rsi_signal(rsi_value)['strength'],
                    'description': self._get_rsi_signal(rsi_value)['description']
                },
                'macd': {
                    'name': 'MACD',
                    'value': 0.0,
                    'signal': 'NEUTRAL',
                    'strength': 'WEAK',
                    'description': 'MACD计算需要ta库支持'
                },
                'ma': {
                    'name': '移动平均',
                    'value': float(ma_5),
                    'signal': self._get_ma_signal(current_price, ma_5, ma_20)['signal'],
                    'strength': self._get_ma_signal(current_price, ma_5, ma_20)['strength'],
                    'description': self._get_ma_signal(current_price, ma_5, ma_20)['description']
                },
                'bollinger': {
                    'name': '布林带',
                    'value': float(current_price),
                    'signal': 'NEUTRAL',
                    'strength': 'WEAK',
                    'description': '布林带计算需要ta库支持'
                },
                'volume': {
                    'name': '成交量',
                    'value': float(df['vol'].iloc[-1]) if 'vol' in df.columns else 0.0,
                    'signal': 'NEUTRAL',
                    'strength': 'WEAK',
                    'description': '成交量分析简化版'
                },
                'momentum': {
                    'name': '动量',
                    'value': float(momentum),
                    'signal': self._get_momentum_signal(momentum)['signal'],
                    'strength': self._get_momentum_signal(momentum)['strength'],
                    'description': self._get_momentum_signal(momentum)['description']
                }
            }
        except Exception as e:
            logger.error(f"简化指标计算失败: {str(e)}")
            return self._get_default_indicators()

    def _get_default_indicators(self) -> Dict[str, Any]:
        """获取默认技术指标"""
        return {
            'rsi': {
                'name': 'RSI',
                'value': 50.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'RSI数据不可用'
            },
            'macd': {
                'name': 'MACD',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'MACD数据不可用'
            },
            'ma': {
                'name': '移动平均',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '移动平均数据不可用'
            },
            'bollinger': {
                'name': '布林带',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '布林带数据不可用'
            },
            'volume': {
                'name': '成交量',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量数据不可用'
            },
            'momentum': {
                'name': '动量',
                'value': 0.0,
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '动量数据不可用'
            }
        }

    def _get_rsi_signal(self, rsi: float) -> Dict[str, str]:
        """RSI信号分析"""
        if pd.isna(rsi):
            rsi = 50.0

        if rsi > 70:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if rsi > 80 else 'MODERATE',
                'description': f'RSI={rsi:.1f}，超买区域，考虑减仓'
            }
        elif rsi < 30:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if rsi < 20 else 'MODERATE',
                'description': f'RSI={rsi:.1f}，超卖区域，考虑买入'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': f'RSI={rsi:.1f}，处于正常区间'
            }

    def _get_macd_signal(self, macd_line: float, signal_line: float, histogram: float) -> Dict[str, str]:
        """MACD信号分析"""
        if any(pd.isna([macd_line, signal_line, histogram])):
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'MACD数据不足'
            }

        if macd_line > signal_line and histogram > 0:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if histogram > 0.5 else 'MODERATE',
                'description': 'MACD金叉，上涨趋势'
            }
        elif macd_line < signal_line and histogram < 0:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if histogram < -0.5 else 'MODERATE',
                'description': 'MACD死叉，下跌趋势'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': 'MACD信号不明确'
            }

    def _get_ma_signal(self, price: float, ma_5: float, ma_20: float) -> Dict[str, str]:
        """移动平均线信号分析"""
        if any(pd.isna([price, ma_5, ma_20])):
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '均线数据不足'
            }

        if price > ma_5 > ma_20:
            return {
                'signal': 'BUY',
                'strength': 'STRONG',
                'description': '价格突破短期均线，多头排列'
            }
        elif price < ma_5 < ma_20:
            return {
                'signal': 'SELL',
                'strength': 'STRONG',
                'description': '价格跌破短期均线，空头排列'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'MODERATE',
                'description': '价格在均线附近震荡'
            }

    def _get_bollinger_signal(self, price: float, upper: float, lower: float, middle: float) -> Dict[str, str]:
        """布林带信号分析"""
        if any(pd.isna([price, upper, lower, middle])):
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '布林带数据不足'
            }

        if price > upper:
            return {
                'signal': 'SELL',
                'strength': 'MODERATE',
                'description': '价格突破上轨，可能回调'
            }
        elif price < lower:
            return {
                'signal': 'BUY',
                'strength': 'MODERATE',
                'description': '价格跌破下轨，可能反弹'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '价格在布林带内正常波动'
            }

    def _get_volume_signal(self, current_volume: float, avg_volume: float) -> Dict[str, str]:
        """成交量信号分析"""
        if pd.isna(current_volume) or pd.isna(avg_volume) or avg_volume == 0:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量数据不足'
            }

        volume_ratio = current_volume / avg_volume
        if volume_ratio > 1.5:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if volume_ratio > 2 else 'MODERATE',
                'description': f'成交量放大{volume_ratio:.1f}倍，关注度提升'
            }
        elif volume_ratio < 0.5:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量萎缩，市场观望'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '成交量正常'
            }

    def _get_momentum_signal(self, momentum: float) -> Dict[str, str]:
        """动量信号分析"""
        if pd.isna(momentum):
            momentum = 0.0

        if momentum > 5:
            return {
                'signal': 'BUY',
                'strength': 'STRONG' if momentum > 10 else 'MODERATE',
                'description': f'动量强劲({momentum:.1f}%)，上涨趋势'
            }
        elif momentum < -5:
            return {
                'signal': 'SELL',
                'strength': 'STRONG' if momentum < -10 else 'MODERATE',
                'description': f'动量疲弱({momentum:.1f}%)，下跌趋势'
            }
        else:
            return {
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'description': '动量平稳'
            }
