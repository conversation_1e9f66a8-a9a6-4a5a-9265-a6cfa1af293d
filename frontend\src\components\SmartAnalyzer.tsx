import React, { useState } from 'react'
import { MagnifyingGlassIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'
import { AnalysisResult } from './AnalysisResult'
import { LoadingSpinner } from './LoadingSpinner'
import { useAnalysis } from '../hooks/useAnalysis'

export const SmartAnalyzer: React.FC = () => {
  const [symbol, setSymbol] = useState('')
  const { analysis, loading, error, analyzeStock } = useAnalysis()

  const handleAnalyze = async () => {
    if (!symbol.trim()) {
      toast.error('请输入股票代码')
      return
    }

    try {
      await analyzeStock(symbol.toUpperCase())
      toast.success('分析完成！')
    } catch (err) {
      toast.error('分析失败，请重试')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAnalyze()
    }
  }

  return (
    <div className="space-y-6">
      {/* 输入区域 */}
      <div className="card">
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary-600 to-purple-600 rounded-full mb-4">
            <SparklesIcon className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            🤖 AI智能分析
          </h2>
          <p className="text-gray-600">
            输入股票代码，一键获得专业分析和投资建议
          </p>
        </div>

        <div className="max-w-md mx-auto">
          <div className="relative">
            <input
              type="text"
              value={symbol}
              onChange={(e) => setSymbol(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入股票代码 (如: AAPL, TSLA)"
              className="input-field pr-12 text-center text-lg font-medium"
              disabled={loading}
            />
            <button
              onClick={handleAnalyze}
              disabled={loading || !symbol.trim()}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-primary-600 hover:text-primary-700 disabled:text-gray-400 transition-colors duration-200"
            >
              {loading ? (
                <LoadingSpinner />
              ) : (
                <MagnifyingGlassIcon className="w-5 h-5" />
              )}
            </button>
          </div>

          <button
            onClick={handleAnalyze}
            disabled={loading || !symbol.trim()}
            className="w-full mt-4 btn-primary text-lg py-3 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <span className="flex items-center justify-center space-x-2">
                <LoadingSpinner />
                <span>AI分析中...</span>
              </span>
            ) : (
              '🚀 开始分析'
            )}
          </button>
        </div>

        {/* 快速选择 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500 mb-3">热门股票快速分析</p>
          <div className="flex flex-wrap justify-center gap-2">
            {['AAPL', 'TSLA', 'GOOGL', 'MSFT', 'AMZN', 'NVDA'].map((stock) => (
              <button
                key={stock}
                onClick={() => setSymbol(stock)}
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors duration-200"
                disabled={loading}
              >
                {stock}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="card border-danger-200 bg-danger-50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-danger-100 rounded-full flex items-center justify-center">
              <span className="text-danger-600 text-sm">⚠️</span>
            </div>
            <div>
              <h3 className="text-danger-800 font-medium">分析失败</h3>
              <p className="text-danger-600 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 分析结果 */}
      {analysis && <AnalysisResult analysis={analysis} />}
    </div>
  )
}
