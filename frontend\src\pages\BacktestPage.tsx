import React, { useState } from 'react'
import { BackwardIcon, PlayIcon } from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { toast } from 'react-hot-toast'

export const BacktestPage: React.FC = () => {
  const [symbol, setSymbol] = useState('')
  const [strategy, setStrategy] = useState('ma_crossover')
  const [loading, setLoading] = useState(false)
  const [backtest, setBacktest] = useState<any>(null)

  const strategies = [
    { value: 'ma_crossover', label: '移动平均交叉', description: '基于短期和长期移动平均线的交叉信号' },
    { value: 'rsi_strategy', label: 'RSI策略', description: '基于相对强弱指数的超买超卖策略' },
    { value: 'macd_strategy', label: 'MACD策略', description: '基于MACD指标的趋势跟踪策略' },
    { value: 'bollinger_bands', label: '布林带策略', description: '基于布林带的均值回归策略' },
  ]

  const handleBacktest = async () => {
    if (!symbol.trim()) {
      toast.error('请输入股票代码')
      return
    }

    setLoading(true)
    try {
      // TODO: 实现回测API调用
      await new Promise(resolve => setTimeout(resolve, 3000)) // 模拟API调用
      
      // 模拟回测数据
      setBacktest({
        symbol: symbol.toUpperCase(),
        strategy: strategy,
        period: '1年',
        total_return: 15.6,
        annual_return: 15.6,
        sharpe_ratio: 1.23,
        max_drawdown: -8.5,
        win_rate: 62.5,
        total_trades: 24,
        winning_trades: 15,
        losing_trades: 9,
        avg_win: 4.2,
        avg_loss: -2.8,
        profit_factor: 2.25,
        performance_data: [
          { date: '2023-01', value: 100 },
          { date: '2023-02', value: 105 },
          { date: '2023-03', value: 98 },
          { date: '2023-04', value: 112 },
          { date: '2023-05', value: 108 },
          { date: '2023-06', value: 115 },
          { date: '2023-07', value: 110 },
          { date: '2023-08', value: 118 },
          { date: '2023-09', value: 114 },
          { date: '2023-10', value: 122 },
          { date: '2023-11', value: 119 },
          { date: '2023-12', value: 115.6 },
        ]
      })
      
      toast.success('回测完成！')
    } catch (err) {
      toast.error('回测失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          📊 策略回测
        </h1>
        <p className="text-gray-600">
          验证交易策略的历史表现
        </p>
      </div>

      {/* 回测设置 */}
      <div className="card">
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-600 to-red-600 rounded-full mb-4">
            <BackwardIcon className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            回测配置
          </h2>
          <p className="text-gray-600">
            选择股票和策略，一键开始回测
          </p>
        </div>

        <div className="max-w-2xl mx-auto space-y-6">
          {/* 股票代码 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              股票代码
            </label>
            <input
              type="text"
              value={symbol}
              onChange={(e) => setSymbol(e.target.value)}
              placeholder="输入股票代码 (如: AAPL, TSLA)"
              className="input-field text-center text-lg font-medium"
              disabled={loading}
            />
          </div>

          {/* 策略选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              交易策略
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {strategies.map((strat) => (
                <label
                  key={strat.value}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    strategy === strat.value
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="strategy"
                    value={strat.value}
                    checked={strategy === strat.value}
                    onChange={(e) => setStrategy(e.target.value)}
                    className="sr-only"
                  />
                  <div className="font-medium text-gray-900 mb-1">
                    {strat.label}
                  </div>
                  <div className="text-sm text-gray-600">
                    {strat.description}
                  </div>
                </label>
              ))}
            </div>
          </div>

          <button
            onClick={handleBacktest}
            disabled={loading || !symbol.trim()}
            className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <span className="flex items-center justify-center space-x-2">
                <LoadingSpinner />
                <span>回测中...</span>
              </span>
            ) : (
              <span className="flex items-center justify-center space-x-2">
                <PlayIcon className="w-5 h-5" />
                <span>开始回测</span>
              </span>
            )}
          </button>
        </div>
      </div>

      {/* 回测结果 */}
      {backtest && (
        <div className="space-y-6 animate-fade-in">
          {/* 核心指标 */}
          <div className="card">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              📈 {backtest.symbol} 回测结果
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {backtest.total_return > 0 ? '+' : ''}{backtest.total_return.toFixed(1)}%
                </div>
                <div className="text-sm text-green-700">总收益率</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {backtest.sharpe_ratio.toFixed(2)}
                </div>
                <div className="text-sm text-blue-700">夏普比率</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {backtest.max_drawdown.toFixed(1)}%
                </div>
                <div className="text-sm text-red-700">最大回撤</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {backtest.win_rate.toFixed(1)}%
                </div>
                <div className="text-sm text-purple-700">胜率</div>
              </div>
            </div>

            {/* 交易统计 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">📊 交易统计</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">总交易次数:</span>
                    <span className="font-medium">{backtest.total_trades}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">盈利交易:</span>
                    <span className="font-medium text-green-600">{backtest.winning_trades}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">亏损交易:</span>
                    <span className="font-medium text-red-600">{backtest.losing_trades}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">平均盈利:</span>
                    <span className="font-medium text-green-600">+{backtest.avg_win.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">平均亏损:</span>
                    <span className="font-medium text-red-600">{backtest.avg_loss.toFixed(1)}%</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">🎯 策略评估</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">策略类型:</span>
                    <span className="font-medium">
                      {strategies.find(s => s.value === backtest.strategy)?.label}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">回测周期:</span>
                    <span className="font-medium">{backtest.period}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">年化收益:</span>
                    <span className="font-medium text-green-600">
                      {backtest.annual_return.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">盈亏比:</span>
                    <span className="font-medium">{backtest.profit_factor.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 策略说明 */}
      <div className="card bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
        <h3 className="text-lg font-bold text-gray-900 mb-4">
          📚 回测说明
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">📊 关键指标</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <strong>总收益率:</strong> 策略的累计收益</li>
              <li>• <strong>夏普比率:</strong> 风险调整后收益</li>
              <li>• <strong>最大回撤:</strong> 最大亏损幅度</li>
              <li>• <strong>胜率:</strong> 盈利交易占比</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">⚠️ 注意事项</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 历史表现不代表未来收益</li>
              <li>• 考虑交易成本和滑点</li>
              <li>• 注意市场环境变化</li>
              <li>• 建议组合多种策略</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
